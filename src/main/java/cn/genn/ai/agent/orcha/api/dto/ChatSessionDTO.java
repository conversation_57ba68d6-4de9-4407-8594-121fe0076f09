package cn.genn.ai.agent.orcha.api.dto;

import cn.genn.ai.agent.orcha.shared.enums.ChatMode;
import cn.genn.ai.agent.orcha.shared.enums.ChatSourceType;
import cn.genn.core.model.enums.BooleanTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 聊天会话DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@Schema(description = "聊天会话信息")
public class ChatSessionDTO {

    @Schema(description = "会话ID")
    private String chatId;

    @Schema(description = "智能体ID")
    private String appId;

    @Schema(description = "会话标题")
    private String title;

    @Schema(description = "聊天模式")
    private ChatMode chatMode;

    @Schema(description = "额外配置")
    private String extraConfig;

    @Schema(description = "是否置顶")
    private BooleanTypeEnum pinned;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "最后更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "对话来源类型")
    private ChatSourceType sourceType;

    @Schema(description = "用户是否已读")
    private BooleanTypeEnum userReadStatus;
}
