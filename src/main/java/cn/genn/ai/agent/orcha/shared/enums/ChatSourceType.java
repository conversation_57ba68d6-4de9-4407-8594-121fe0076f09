package cn.genn.ai.agent.orcha.shared.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 对话来源类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ChatSourceType {

    /**
     * 用户主动发起
     */
    USER_INITIATED("user", "用户主动发起"),

    /**
     * 定时任务生成
     */
    SCHEDULED_TASK("scheduled", "定时任务生成");

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    /**
     * 根据code获取枚举
     */
    public static ChatSourceType fromCode(String code) {
        for (ChatSourceType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown ChatSourceType code: " + code);
    }
}
