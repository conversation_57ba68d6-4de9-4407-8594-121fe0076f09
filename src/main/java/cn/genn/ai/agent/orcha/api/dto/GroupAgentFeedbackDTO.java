package cn.genn.ai.agent.orcha.api.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GroupAgentFeedbackDTO {

    @Schema(description = "工作流ID")
    @JsonProperty("appId")  // 序列化时使用这个名称
    @JsonAlias({"appId", "workflowId"})  // 反序列化时这些名称都可以接受
    private String appId;

    @Schema(description = "聊天ID")
    private String chatId;

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "反馈列表")
    private List<AgentFeedbackDTO> feedbackList;
}
