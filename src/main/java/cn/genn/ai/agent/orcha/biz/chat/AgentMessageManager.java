package cn.genn.ai.agent.orcha.biz.chat;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ChatHistoryDTO;
import cn.genn.ai.agent.orcha.api.dto.ChatSessionDTO;
import cn.genn.ai.agent.orcha.api.query.ChatSessionQuery;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatMessage;
import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import cn.genn.core.model.page.PageResultDTO;

import java.util.List;

/**
 * Agent 消息管理器接口
 *
 * 负责Agent对话消息的存储、检索和断线重连
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AgentMessageManager {

    /**
     * 存储Agent消息
     *
     * @param message Agent消息
     */
    void storeMessage(AgentMessage message);

    /**
     * 获取指定会话的最近一条消息
     *
     * @param chatId 会话ID
     * @return 消息流
     */
    AgentChatMessage getLastedMessage(String chatId);

    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    TaskStatus getTaskStatus(String taskId);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     */
    void updateTaskStatus(String chatId, String taskId, TaskStatus status);

    /**
     * 根据chatId查询聊天记录
     *
     * @param chatId 会话ID
     * @return 聊天记录列表
     */
    List<ChatHistoryDTO> getChatHistory(String chatId);

    /**
     * 获取用户的聊天会话列表
     *
     * @return 聊天会话列表
     */
    PageResultDTO<ChatSessionDTO> getChatSessions(ChatSessionQuery query);

    /**
     * 获取指定会话
     *
     * @param chatId 会话ID
     */
    void deleteChatSession(String chatId);

    /**
     * 置顶/取消置顶会话
     *
     * @param chatId 会话ID
     * @param pinned 是否置顶
     */
    void pinChatSession(String chatId, boolean pinned);

    /**
     * 修改会话标题
     *
     * @param chatId 会话ID
     * @param title 新标题
     */
    void updateChatSessionTitle(String chatId, String title);

    /**
     * 获取指定会话的消息数量
     *
     * @param chatId 会话ID
     * @return 消息数量
     */
    Long getChatSessionCount(String chatId);

    /**
     * 标记会话为已读
     *
     * @param chatId 会话ID
     */
    void markChatSessionAsRead(String chatId);
}
