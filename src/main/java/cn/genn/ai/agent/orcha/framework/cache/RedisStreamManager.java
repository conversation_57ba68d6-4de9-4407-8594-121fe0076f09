package cn.genn.ai.agent.orcha.framework.cache;

import cn.genn.ai.agent.orcha.shared.constants.CacheConstants;
import cn.genn.ai.agent.orcha.shared.exception.AgentOrchaMessageCode;
import cn.genn.core.exception.BaseException;
import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Range;
import org.springframework.data.redis.connection.stream.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Redis Stream 管理器
 *
 * 封装所有与 Redis Stream 相关的操作，用于替代原有的 Sorted Set + Pub/Sub 架构
 * 提供消息存储、读取、裁剪等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisStreamManager {

    private final RedisTemplate<String, Object> redisTemplate;
    /**
     * 本地缓存，用于记录已设置过期时间的流key
     * 缓存过期时间与Redis流的过期时间保持一致
     */
    private final Cache<String, Boolean> expiredKeysCache = Caffeine.newBuilder()
        .maximumSize(10000) // 最大缓存10000个key
        .expireAfterWrite(Duration.ofSeconds(CacheConstants.AGENT_STREAM_TTL)) // 与Redis过期时间一致
        .build();


    /**
     * 添加消息到流中
     *
     * @param taskId 任务ID
     * @param messageData 消息数据
     * @return Redis 生成的消息ID
     */
    public String addMessage(String taskId, Map<String, String> messageData) {
        if (StrUtil.isBlank(taskId) || messageData == null || messageData.isEmpty()) {
            log.warn("添加消息参数无效，taskId: {}, messageData: {}", taskId, messageData);
            return null;
        }

        String key = CacheConstants.buildAgentStreamKey(taskId);

        try {
            // 使用 XADD 命令添加消息到流中，Redis 自动生成消息ID
            RecordId recordId = redisTemplate.opsForStream().add(key, messageData);

            // 检查本地缓存，如果key不存在则设置Redis过期时间
            if (expiredKeysCache.getIfPresent(key) == null) {
                redisTemplate.expire(key, Duration.ofSeconds(CacheConstants.AGENT_STREAM_TTL));
                expiredKeysCache.put(key, Boolean.TRUE);
                log.debug("为流 {} 设置过期时间", taskId);
            }

            String messageId = recordId.getValue();
            log.debug("添加消息到流 {}，消息ID: {}", taskId, messageId);

            return messageId;

        } catch (Exception e) {
            log.error("添加消息到流失败，taskId: {}", taskId, e);
            return null;
        }

    }

    /**
     * 从指定位置开始读取消息（阻塞式）
     *
     * @param taskId 任务ID
     * @param lastId 最后一条消息的ID，如果是新客户端应传入 "0-0"
     * @param blockTimeout 阻塞超时时间（毫秒），0表示无限等待
     * @return 读取到的消息列表
     */
    public List<MapRecord<String, Object, Object>> readMessages(String taskId, String lastId, long blockTimeout) {
        if (StrUtil.isBlank(taskId)) {
            log.warn("读取消息参数无效，taskId: {}", taskId);
            return Collections.emptyList();
        }

        String key = CacheConstants.buildAgentStreamKey(taskId);
        String startId = StrUtil.isBlank(lastId) ? "0-0" : lastId;

        try {
            // 使用 XREAD 命令阻塞式读取消息
            StreamOffset<String> streamOffset = StreamOffset.create(key, ReadOffset.from(startId));
            StreamReadOptions options = StreamReadOptions.empty();

            if (blockTimeout > 0) {
                options = options.block(Duration.ofMillis(blockTimeout));
            }

            List<MapRecord<String, Object, Object>> records = redisTemplate.opsForStream()
                .read(options, streamOffset);

            log.debug("从流 {} 读取消息，起始ID: {}, 读取数量: {}", taskId, startId,
                records != null ? records.size() : 0);

            return records != null ? records : Collections.emptyList();

        } catch (Exception e) {
            log.error("从流读取消息失败，taskId: {}, lastId: {}", taskId, lastId, e);
            throw new BaseException(AgentOrchaMessageCode.REDIS_STREAM_READ_ERROR);
        }
    }

    /**
     * 读取指定范围的历史消息
     *
     * @param taskId 任务ID
     * @param start 起始ID，null表示从头开始
     * @param end 结束ID，null表示到最新
     * @param count 最大返回数量，-1表示不限制
     * @return 消息列表
     */
    public List<MapRecord<String, Object, Object>> readRange(String taskId, String start, String end, long count) {
        if (StrUtil.isBlank(taskId)) {
            log.warn("读取范围消息参数无效，taskId: {}", taskId);
            return Collections.emptyList();
        }

        String key = CacheConstants.buildAgentStreamKey(taskId);

        try {
            Range<String> range;
            if (start == null && end == null) {
                range = Range.unbounded();
            } else if (start == null) {
                range = Range.rightUnbounded(Range.Bound.inclusive(end));
            } else if (end == null) {
                range = Range.leftUnbounded(Range.Bound.inclusive(start));
            } else {
                range = Range.closed(start, end);
            }

            List<MapRecord<String, Object, Object>> records;
            if (count > 0) {
                records = redisTemplate.opsForStream().range(key, range, org.springframework.data.redis.connection.Limit.limit().count((int) count));
            } else {
                records = redisTemplate.opsForStream().range(key, range);
            }

            log.debug("从流 {} 读取范围消息，范围: [{}, {}], 数量: {}", taskId, start, end,
                records != null ? records.size() : 0);

            return records != null ? records : Collections.emptyList();

        } catch (Exception e) {
            log.error("读取范围消息失败，taskId: {}, 范围: [{}, {}]", taskId, start, end, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取流中的所有消息
     *
     * @param taskId 任务ID
     * @return 所有消息列表
     */
    public List<MapRecord<String, Object, Object>> getAllMessages(String taskId) {
        return readRange(taskId, null, null, -1);
    }

    /**
     * 裁剪流，防止无限增长
     *
     * @param taskId 任务ID
     * @param maxLength 保留的最大消息数量
     */
    public void trimStream(String taskId, long maxLength) {
        if (StrUtil.isBlank(taskId) || maxLength <= 0) {
            log.warn("裁剪流参数无效，taskId: {}, maxLength: {}", taskId, maxLength);
            return;
        }

        String key = CacheConstants.buildAgentStreamKey(taskId);

        try {
            // 使用 XTRIM 命令裁剪流
            Long trimmed = redisTemplate.opsForStream().trim(key, maxLength);

            log.debug("裁剪流 {}，保留长度: {}, 删除数量: {}", taskId, maxLength, trimmed);

        } catch (Exception e) {
            log.error("裁剪流失败，taskId: {}, maxLength: {}", taskId, maxLength, e);
        }
    }

    /**
     * 裁剪流（使用默认配置）
     *
     * @param taskId 任务ID
     */
    public void trimStream(String taskId) {
        trimStream(taskId, CacheConstants.TRIM_STREAM_SIZE);
    }

    /**
     * 删除整个流
     *
     * @param taskId 任务ID
     */
    public void deleteStream(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            log.warn("删除流参数无效，taskId: {}", taskId);
            return;
        }

        String key = CacheConstants.buildAgentStreamKey(taskId);

        try {
            Boolean deleted = redisTemplate.delete(key);
            log.info("删除流 {}，结果: {}", taskId, deleted);

        } catch (Exception e) {
            log.error("删除流失败，taskId: {}", taskId, e);
        }
    }

    /**
     * 检查流是否存在
     *
     * @param taskId 任务ID
     * @return 是否存在
     */
    public boolean existsStream(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            return false;
        }

        String key = CacheConstants.buildAgentStreamKey(taskId);

        try {
            Boolean exists = redisTemplate.hasKey(key);
            return Boolean.TRUE.equals(exists);

        } catch (Exception e) {
            log.error("检查流存在性失败，taskId: {}", taskId, e);
            return false;
        }
    }

    /**
     * 获取流的长度
     *
     * @param taskId 任务ID
     * @return 流中的消息数量
     */
    public Long getStreamLength(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            return 0L;
        }

        String key = CacheConstants.buildAgentStreamKey(taskId);

        try {
            Long length = redisTemplate.opsForStream().size(key);
            log.debug("流 {} 长度: {}", taskId, length);
            return length != null ? length : 0L;

        } catch (Exception e) {
            log.error("获取流长度失败，taskId: {}", taskId, e);
            return 0L;
        }
    }

    /**
     * 创建消费者组（为未来扩展预留）
     *
     * @param taskId 任务ID
     * @param groupName 消费者组名称
     * @param startId 起始消息ID，"0"表示从头开始，"$"表示从最新消息开始
     */
    public void createConsumerGroup(String taskId, String groupName, String startId) {
        if (StrUtil.isBlank(taskId) || StrUtil.isBlank(groupName)) {
            log.warn("创建消费者组参数无效，taskId: {}, groupName: {}", taskId, groupName);
            return;
        }

        String key = CacheConstants.buildAgentStreamKey(taskId);
        String actualStartId = StrUtil.isBlank(startId) ? "0" : startId;

        try {
            redisTemplate.opsForStream().createGroup(key, ReadOffset.from(actualStartId), groupName);
            log.info("为流 {} 创建消费者组: {}, 起始ID: {}", taskId, groupName, actualStartId);

        } catch (Exception e) {
            log.error("创建消费者组失败，taskId: {}, groupName: {}", taskId, groupName, e);
        }
    }
}
