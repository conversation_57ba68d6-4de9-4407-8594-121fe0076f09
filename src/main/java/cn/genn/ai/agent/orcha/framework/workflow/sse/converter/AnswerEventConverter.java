package cn.genn.ai.agent.orcha.framework.workflow.sse.converter;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.framework.workflow.sse.CerebroEventType;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagBufferManager;
import cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml.XmlTagParser;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * answer事件转换器
 * <p>
 * 负责解析answer事件中的XML标签内容，如thinking、question、step等
 * 使用策略模式处理不同类型的标签，支持跨chunk的不完整标签处理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class AnswerEventConverter extends BaseEventConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String DONE = "[DONE]";

    @Resource
    private XmlTagParser xmlTagParser;

    @Override
    public boolean supports(String eventType) {
        return CerebroEventType.ANSWER.getCode().equals(eventType) ||
            CerebroEventType.FAST_ANSWER.getCode().equals(eventType);
    }

    @Override
    public List<AgentMessage> convert(ServerSentEvent<String> event, ExecutionRequest request, String taskId) {
        try {
            String data = event.data();
            if (StrUtil.isBlank(data)) {
                return null;
            }
            if (DONE.equals(data)) {
                return null;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = OBJECT_MAPPER.readValue(data, Map.class);

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> choices = (List<Map<String, Object>>) dataMap.get("choices");
            if (choices == null || choices.isEmpty()) {
                return null;
            }

            Map<String, Object> choice = choices.getFirst();
            @SuppressWarnings("unchecked")
            Map<String, Object> delta = (Map<String, Object>) choice.get("delta");
            if (delta == null) {
                return null;
            }

            String reasonContent = (String) delta.get("reasoning_content");
            String content = (String) delta.get("content");

            if (StrUtil.isNotEmpty(reasonContent) && !XmlTagBufferManager.getModelThinkSign(taskId)) {
                XmlTagBufferManager.setModelThinkSign(taskId, true);
                content = "<think>" + reasonContent;
            }else if (StrUtil.isNotEmpty(content) && XmlTagBufferManager.getModelThinkSign(taskId)) {
                content = "</think>" + content;
                XmlTagBufferManager.setModelThinkSign(taskId, false);
            }else if (StrUtil.isNotEmpty(reasonContent)) {
                content = reasonContent;
            }

            if (StrUtil.isEmpty(content)) {
                return null;
            }

            // 直接使用XML解析器处理内容，区域逻辑在解析时已处理
            List<AgentMessage> messages = xmlTagParser.parse(content, taskId, request);

            return CollUtil.isEmpty(messages) ? null : messages;

        } catch (Exception e) {
            log.error("转换回答事件失败", e);
            return null;
        }
    }


    @Override
    public int getOrder() {
        return 30;
    }
}
