package cn.genn.ai.agent.orcha.framework.workflow;

import cn.genn.ai.agent.orcha.framework.workflow.dto.WorkflowRequest;
import cn.genn.ai.agent.orcha.framework.workflow.dto.WorkflowResponse;
import reactor.core.publisher.Flux;

/**
 * 工作流客户端接口
 *
 * 定义与 Cerebro 工作流系统的交互规范
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface WorkflowClient {

    /**
     * 流式执行工作流（原始SSE数据）
     *
     * @param request 工作流请求
     * @return 工作流响应流，第一个元素包含响应头信息（taskId等），后续元素包含SSE数据
     */
    Flux<WorkflowResponse> executeStreamRaw(WorkflowRequest request);

    /**
     * 停止工作流执行
     *
     * @param taskId 执行ID
     * @return 是否成功停止
     */
    boolean stopExecution(String appId, String taskId);

}
