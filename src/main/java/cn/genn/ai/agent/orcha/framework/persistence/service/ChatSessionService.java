package cn.genn.ai.agent.orcha.framework.persistence.service;

import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatSession;
import cn.genn.ai.agent.orcha.framework.persistence.mapper.AgentChatSessionMapper;
import cn.genn.ai.agent.orcha.shared.enums.ChatSourceType;
import cn.genn.core.model.enums.BooleanTypeEnum;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 聊天会话服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatSessionService {

    private final AgentChatSessionMapper sessionMapper;

    /**
     * 根据 chatId 获取聊天会话
     */
    public AgentChatSession getChatSession(String chatId) {
        LambdaQueryWrapper<AgentChatSession> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentChatSession::getChatId, chatId);
        return sessionMapper.selectOne(queryWrapper);
    }

    /**
     * 初始化聊天会话
     * 在任务初始化提交时调用，确保会话记录存在
     *
     * @param request 执行请求
     */
    @Transactional(rollbackFor = Exception.class)
    public void initializeChatSession(ExecutionRequest request) {
        try {
            String chatId = request.getChatId();

            // 检查会话是否已存在
            LambdaQueryWrapper<AgentChatSession> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AgentChatSession::getChatId, chatId);

            AgentChatSession existingSession = sessionMapper.selectOne(queryWrapper);
            if (existingSession != null) {
                log.debug("聊天会话已存在，chatId: {}", chatId);
                return;
            }

            // 根据对话来源类型设置已读状态
            ChatSourceType sourceType = request.getSource();
            BooleanTypeEnum readStatus = (sourceType == ChatSourceType.SCHEDULED_TASK) ?
                BooleanTypeEnum.FALSE : BooleanTypeEnum.TRUE;

            // 创建新的会话记录
            AgentChatSession session = new AgentChatSession()
                .setChatId(chatId)
                .setAppId(request.getAppId())
                .setTitle(extractSessionTitleFromRequest(request))
                .setChatMode(request.getChatMode())
                .setExtraConfig(extractExtraConfigFromRequest(request))
                .setTenantId(request.getUserInfo().getTenantId())
                .setCreateUserId(request.getUserInfo().getUserId())
                .setCreateUserName(request.getUserInfo().getUsername())
                .setSourceType(sourceType)
                .setUserReadStatus(readStatus);

            sessionMapper.insert(session);
            log.info("创建聊天会话记录，chatId: {}, appId: {}", chatId, session.getAppId());

        } catch (Exception e) {
            log.error("初始化聊天会话失败，chatId: {}", request.getChatId(), e);
            throw e;
        }
    }

    /**
     * 从请求中提取会话标题
     */
    private String extractSessionTitleFromRequest(ExecutionRequest request) {
        if (StrUtil.isNotBlank(request.getFormattedInput()) && request.getFormattedInput().length() <= 30) {
            return request.getFormattedInput();
        } else if (StrUtil.isNotBlank(request.getFormattedInput())) {
            return request.getFormattedInput().substring(0, 30);
        }
        return "新对话";
    }

    /**
     * 从请求中提取额外配置
     */
    private String extractExtraConfigFromRequest(ExecutionRequest request) {
        if (request.getParameters() != null && !request.getParameters().isEmpty()) {
            try {
                return JsonUtils.toJson(request.getParameters());
            } catch (Exception e) {
                log.warn("序列化额外配置失败", e);
                return null;
            }
        }
        return null;
    }
}
