package cn.genn.ai.agent.orcha.framework.config.properties;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CerebroWorkflowProperties {

    /**
     * cerebro工作流服务地址
     */
    private String workflowUrl;

    /**
     * cerebro工作流服务API Key
     */
    private String workflowAuthKey = "gennai-internal-api-key-4vQYhr7VPLTQyV9X4GRPQgmRrDg4Kb7qsdGP";

    /**
     * gennAIHub地址
     */
    private String gennAIHubUrl = "http://genn-ai-hub/api/ai";

    /**
     * 验证定时任务免登来源
     */
    private String magicCheckUUID = "GStotyihChhMTdH6UbrU0usNd1r3ayIv";
}
