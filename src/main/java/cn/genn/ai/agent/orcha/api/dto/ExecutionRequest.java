package cn.genn.ai.agent.orcha.api.dto;

import cn.genn.ai.agent.orcha.shared.enums.ChatMode;
import cn.genn.ai.agent.orcha.shared.enums.ChatSourceType;
import cn.genn.ai.agent.orcha.shared.enums.ExecutionMode;
import cn.genn.spring.boot.starter.upm.model.SsoUserAuthInfoDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 执行请求 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "执行请求")
public class ExecutionRequest {

    @Schema(description = "执行模式", example = "workflow")
    private ExecutionMode mode = ExecutionMode.WORKFLOW;

    @Schema(description = "聊天模式", example = "common")
    private ChatMode chatMode;

    @Schema(description = "用户输入", example = "帮我分析这个数据")
    private String input;

    @Schema(description = "格式化后的输入", example = "text")
    private String formattedInput;

    @Schema(description = "文件信息")
    private List<FileInfo> fileInfo;

    @Schema(description = "应用ID,自动赋值", example = "app_123")
    private String appId;

    @Schema(description = "聊天ID", example = "chat_123")
    @NotBlank(message = "聊天ID不能为空")
    private String chatId;

    @Schema(description = "任务ID,断点重连,交互式节点需要携带", example = "task_123")
    private String taskId;

    @Schema(description = "客户端收到的最后一条消息的ID (Redis Stream's Entry ID)，用于断线重连。如果是新会话，则忽略此字段。", example = "1720786800000-0")
    private String lastEventId;

    @Schema(description = "响应数据的dataId")
    private String responseChatItemId;

    @Schema(description = "扩展参数")
    private Map<String, String> parameters;

    @Schema(description = "是否流式返回", example = "true")
    private Boolean stream = true;

    @Schema(description = "用户信息")
    @JsonIgnore
    private SsoUserAuthInfoDTO userInfo;

    @JsonIgnore
    private Map<String, String> httpHeaders;

    /**
     * 是否是新会话
     */
    private boolean newTask;

    /**
     * 对话来源类型（通过magicToken等判断）
     */
    private ChatSourceType source = ChatSourceType.USER_INITIATED;

    /**
     * Magic Token
     */
    @JsonIgnore
    private String magicToken;
}
