package cn.genn.ai.agent.orcha.biz.chat.impl;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ChatHistoryDTO;
import cn.genn.ai.agent.orcha.api.dto.ChatSessionDTO;
import cn.genn.ai.agent.orcha.api.dto.GroupAgentFeedbackDTO;
import cn.genn.ai.agent.orcha.api.query.ChatSessionQuery;
import cn.genn.ai.agent.orcha.biz.chat.AgentMessageManager;
import cn.genn.ai.agent.orcha.framework.cache.AgentCacheManager;
import cn.genn.ai.agent.orcha.framework.cache.RedisStreamManager;
import cn.genn.ai.agent.orcha.framework.persistence.converter.AgentChatSessionConverter;
import cn.genn.ai.agent.orcha.framework.persistence.converter.AgentMessageConverter;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatMessage;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatSession;
import cn.genn.ai.agent.orcha.framework.persistence.mapper.AgentChatMessageMapper;
import cn.genn.ai.agent.orcha.framework.persistence.mapper.AgentChatSessionMapper;
import cn.genn.ai.agent.orcha.framework.persistence.service.MessagePersistenceService;
import cn.genn.ai.agent.orcha.framework.persistence.utils.message.MergedMessage;
import cn.genn.ai.agent.orcha.shared.constants.CacheConstants;
import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import cn.genn.ai.agent.orcha.shared.exception.AgentOrchaMessageCode;
import cn.genn.ai.agent.orcha.shared.third.GennAIHubClient;
import cn.genn.core.exception.BaseException;
import cn.genn.core.model.enums.BooleanTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Agent消息管理器实现
 * <p>
 * 基于Redis Stream实现消息缓存和流式处理
 * 支持断点重连和高并发场景
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentMessageManagerImpl implements AgentMessageManager {

    private final RedisStreamManager redisStreamManager;
    private final AgentCacheManager agentCacheManager;
    private final AgentChatMessageMapper agentChatMessageMapper;
    private final AgentChatSessionMapper agentChatSessionMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private final AgentMessageConverter agentMessageConverter;
    private final AgentChatSessionConverter agentChatSessionConverter;
    private final MessagePersistenceService messagePersistenceService;
    private final GennAIHubClient gennAIHubClient;


    /**
     * 本地任务状态缓存，减少Redis访问
     */
    private final ConcurrentMap<String, TaskStatus> taskStatusCache = new ConcurrentHashMap<>();

    @Override
    public void storeMessage(AgentMessage message) {
        if (message == null) {
            log.warn("存储消息参数为空");
            return;
        }
        try {
            String taskId = message.getTaskId();
            // 将AgentMessage转换为Map
            Map<String, String> messageData = agentMessageConverter.agentMessageToMap(message);

            // 调用RedisStreamManager添加消息到流中
            String messageId = redisStreamManager.addMessage(taskId, messageData);

            // 将返回的messageId设置回message对象
            if (messageId != null) {
                message.setMessageId(messageId);
            }

            log.debug("存储消息成功，taskId: {}, chatId: {}, messageId: {}",
                message.getTaskId(), message.getChatId(), message.getMessageId());

        } catch (Exception e) {
            log.error("存储消息失败，taskId: {}, messageId: {}",
                message.getTaskId(), message.getMessageId(), e);
        }
    }

    @Override
    public AgentChatMessage getLastedMessage(String chatId) {
        List<AgentChatMessage> chatMessages = agentChatMessageMapper.selectList(Wrappers.<AgentChatMessage>lambdaQuery()
            .eq(AgentChatMessage::getChatId, chatId)
            .eq(AgentChatMessage::getDeleted, DeletedTypeEnum.NOT_DELETED)
            .orderByDesc(AgentChatMessage::getCreateTime)
            .last("LIMIT 1"));
        if (chatMessages.isEmpty()) {
            return null;
        }
        return chatMessages.getFirst();
    }

    @Override
    public TaskStatus getTaskStatus(String taskId) {
        if (taskId == null) {
            return TaskStatus.FAILED;
        }

        try {
            // 优先从本地缓存获取
            TaskStatus cachedStatus = taskStatusCache.get(taskId);
            if (cachedStatus != null) {
                return cachedStatus;
            }

            // 从Redis获取
            TaskStatus status = agentCacheManager.getTaskStatus(taskId);

            if (status == TaskStatus.UNKNOWN) {
                // 如果缓存中没有，尝试从数据库获取
                List<AgentChatMessage> chatMessages = agentChatMessageMapper.selectList(
                    Wrappers.<AgentChatMessage>lambdaQuery()
                        .select(AgentChatMessage::getId, AgentChatMessage::getTaskStatus)
                        .eq(AgentChatMessage::getTaskId, taskId)
                        .orderByDesc(AgentChatMessage::getCreateTime));
                if (!chatMessages.isEmpty()) {
                    status = chatMessages.getFirst().getTaskStatus();
                }
            }
            // 更新本地缓存
            taskStatusCache.put(taskId, status);

            log.debug("获取任务状态，taskId: {}, status: {}", taskId, status);
            return status;

        } catch (Exception e) {
            log.error("获取任务状态失败，taskId: {}", taskId, e);
            return TaskStatus.FAILED;
        }
    }

    @Override
    public void updateTaskStatus(String chatId, String taskId, TaskStatus status) {
        if (taskId == null || status == null) {
            log.warn("更新任务状态参数无效，taskId: {}, status: {}", taskId, status);
            return;
        }

        try {
            String key = CacheConstants.buildAgentTaskStatusKey(taskId);

            // 更新Redis
            stringRedisTemplate.opsForValue().set(key, status.getCode(),
                Duration.ofSeconds(CacheConstants.AGENT_TASK_STATUS_TTL));

            // 更新本地缓存
            taskStatusCache.put(taskId, status);

            log.debug("更新任务状态，taskId: {}, status: {}", taskId, status);

        } catch (Exception e) {
            log.error("更新任务状态失败，taskId: {}, status: {}", taskId, status, e);
        }
    }

    @Override
    public List<ChatHistoryDTO> getChatHistory(String chatId) {
        if (StrUtil.isBlank(chatId)) {
            log.warn("获取聊天记录参数无效，chatId为空");
            return List.of();
        }

        // 查询指定chatId的所有消息记录，按创建时间排序
        List<AgentChatMessage> chatMessages = new ArrayList<>();
        // 第一步：只查询用于排序的字段和主键
        List<AgentChatMessage> sortedIds = agentChatMessageMapper.selectList(
            Wrappers.<AgentChatMessage>lambdaQuery()
                .select(AgentChatMessage::getId, AgentChatMessage::getCreateTime) // 只查ID和排序字段
                .eq(AgentChatMessage::getChatId, chatId)
                .eq(AgentChatMessage::getTenantId, CurrentUserHolder.getTenantId())
                .eq(AgentChatMessage::getCreateUserId, CurrentUserHolder.getUserId())
                .orderByAsc(AgentChatMessage::getCreateTime)
        );

        // 第二步：根据排序后的ID列表查询完整信息
        if (!sortedIds.isEmpty()) {
            List<Long> ids = sortedIds.stream()
                .map(AgentChatMessage::getId)
                .collect(Collectors.toList());

            chatMessages = agentChatMessageMapper.selectByIds(ids);

            // 保持第一步的排序顺序
            Map<Long, AgentChatMessage> messageMap = chatMessages.stream()
                .collect(Collectors.toMap(AgentChatMessage::getId, Function.identity()));

            chatMessages = ids.stream()
                .map(messageMap::get)
                .toList();
        }


        // 转换为ChatHistoryDTO
        List<ChatHistoryDTO> historyList = agentMessageConverter.chatMessagesToHistory(chatMessages);

        if (!historyList.isEmpty()) {
            ChatHistoryDTO lastMessage = historyList.getLast();
            if (lastMessage.getTaskStatus() == TaskStatus.RUNNING) {
                // 确定起始消息ID
                String lastMessageId = "0-0"; // 默认值
                if (historyList.size() > 1) {
                    ChatHistoryDTO lastCompletedMessage = historyList.get(historyList.size() - 2);
                    if (CharSequenceUtil.isNotEmpty(lastCompletedMessage.getLastMessageId())) {
                        lastMessageId = lastCompletedMessage.getLastMessageId();
                    }
                }

                // 从Redis Stream中获取最新消息
                List<MapRecord<String, Object, Object>> records = redisStreamManager.readMessages(
                    lastMessage.getTaskId(), lastMessageId, 0);
                List<MergedMessage> mergedMessages = messagePersistenceService.buildMergeMessages(records);
                lastMessage.setAnswer(mergedMessages);
                lastMessage.setLastMessageId(mergedMessages.isEmpty() ? null : mergedMessages.getLast().getLastMessageId());
            }
        }


        //填充反馈信息
        if (!historyList.isEmpty()) {
            ChatHistoryDTO peekHistory = historyList.getFirst();
            List<GroupAgentFeedbackDTO> feedbackList = gennAIHubClient.fetchFeedbackList(peekHistory.getAppId(), peekHistory.getChatId(),
                historyList.stream().map(ChatHistoryDTO::getTaskId).distinct().collect(Collectors.toList()));
            Map<String, GroupAgentFeedbackDTO> feedbackMap = feedbackList.stream().collect(Collectors.toMap(GroupAgentFeedbackDTO::getTaskId, Function.identity()));
            historyList.forEach(history -> {
                GroupAgentFeedbackDTO feedback = feedbackMap.get(history.getTaskId());
                if (feedback != null) {
                    history.setFeedback(feedback.getFeedbackList());
                }
            });

        }

        log.info("获取聊天记录成功，chatId: {}, 记录数: {}", chatId, historyList.size());
        return historyList;
    }


    @Override
    public PageResultDTO<ChatSessionDTO> getChatSessions(ChatSessionQuery query) {
        IPage<AgentChatSession> chatSessionPage = agentChatSessionMapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()),
            Wrappers.<AgentChatSession>lambdaQuery()
                .like(CharSequenceUtil.isNotEmpty(query.getTitle()), AgentChatSession::getTitle, query.getTitle())
                .eq(AgentChatSession::getTenantId, CurrentUserHolder.getTenantId())
                .eq(AgentChatSession::getCreateUserId, CurrentUserHolder.getUserId())
                .eq(AgentChatSession::getDeleted, DeletedTypeEnum.NOT_DELETED)
                .orderByDesc(AgentChatSession::getPinned)  // 置顶的会话优先显示
                .orderByDesc(AgentChatSession::getUpdateTime)
        );
        return agentChatSessionConverter.toPageResult(chatSessionPage);
    }

    @Override
    public void deleteChatSession(String chatId) {
        AgentChatSession chatSession = agentChatSessionMapper.selectOne(Wrappers.<AgentChatSession>lambdaQuery().
            eq(AgentChatSession::getChatId, chatId)
            .eq(AgentChatSession::getCreateUserId, CurrentUserHolder.getUserId())
            .eq(AgentChatSession::getDeleted, DeletedTypeEnum.NOT_DELETED));
        if (chatSession == null) {
            throw new BaseException(AgentOrchaMessageCode.CHAT_SESSION_NOT_FOUND, chatId);
        }
        AgentChatSession update = new AgentChatSession()
            .setId(chatSession.getId())
            .setDeleted(DeletedTypeEnum.DELETED);

        agentChatSessionMapper.updateById(update);
    }

    @Override
    public void pinChatSession(String chatId, boolean pinned) {
        if (StrUtil.isBlank(chatId)) {
            log.warn("置顶会话参数无效，chatId为空");
            return;
        }

        try {
            // 查找会话
            AgentChatSession chatSession = agentChatSessionMapper.selectOne(
                Wrappers.<AgentChatSession>lambdaQuery()
                    .eq(AgentChatSession::getChatId, chatId)
                    .eq(AgentChatSession::getCreateUserId, CurrentUserHolder.getUserId())
                    .eq(AgentChatSession::getDeleted, DeletedTypeEnum.NOT_DELETED)
            );

            if (chatSession == null) {
                throw new BaseException(AgentOrchaMessageCode.CHAT_SESSION_NOT_FOUND, chatId);
            }

            // 更新置顶状态
            AgentChatSession update = new AgentChatSession()
                .setId(chatSession.getId())
                .setPinned(pinned ? BooleanTypeEnum.TRUE : BooleanTypeEnum.FALSE);

            agentChatSessionMapper.updateById(update);

            log.info("{}会话成功，chatId: {}", pinned ? "置顶" : "取消置顶", chatId);

        } catch (Exception e) {
            log.error("{}会话失败，chatId: {}", pinned ? "置顶" : "取消置顶", chatId, e);
            throw e;
        }
    }

    @Override
    public void updateChatSessionTitle(String chatId, String title) {
        if (StrUtil.isBlank(chatId)) {
            log.warn("修改会话标题参数无效，chatId为空");
            return;
        }

        if (StrUtil.isBlank(title)) {
            log.warn("修改会话标题参数无效，title为空");
            return;
        }

        // 限制标题长度
        if (title.length() > 100) {
            title = title.substring(0, 100);
        }

        try {
            // 查找会话
            AgentChatSession chatSession = agentChatSessionMapper.selectOne(
                Wrappers.<AgentChatSession>lambdaQuery()
                    .eq(AgentChatSession::getChatId, chatId)
                    .eq(AgentChatSession::getCreateUserId, CurrentUserHolder.getUserId())
                    .eq(AgentChatSession::getDeleted, DeletedTypeEnum.NOT_DELETED)
            );

            if (chatSession == null) {
                throw new BaseException(AgentOrchaMessageCode.CHAT_SESSION_NOT_FOUND, chatId);
            }

            // 更新标题
            AgentChatSession update = new AgentChatSession()
                .setId(chatSession.getId())
                .setTitle(title);

            agentChatSessionMapper.updateById(update);

            log.info("修改会话标题成功，chatId: {}, newTitle: {}", chatId, title);

        } catch (Exception e) {
            log.error("修改会话标题失败，chatId: {}, title: {}", chatId, title, e);
            throw e;
        }
    }

    @Override
    public Long getChatSessionCount(String chatId) {
        if (StrUtil.isBlank(chatId)) {
            throw new BaseException(AgentOrchaMessageCode.CHAT_SESSION_NOT_FOUND, chatId);
        }
        // 查询指定chatId的消息数量
        Long count = agentChatMessageMapper.selectCount(
            Wrappers.<AgentChatMessage>lambdaQuery()
                .eq(AgentChatMessage::getChatId, chatId)
                .eq(AgentChatMessage::getDeleted, DeletedTypeEnum.NOT_DELETED)
        );
        log.info("获取会话消息数量成功，chatId: {}, count: {}", chatId, count);
        return count;
    }

    @Override
    public void markChatSessionAsRead(String chatId) {
        if (StrUtil.isBlank(chatId)) {
            log.warn("标记会话已读参数无效，chatId为空");
            return;
        }

        try {
            // 查找会话
            AgentChatSession chatSession = agentChatSessionMapper.selectOne(
                Wrappers.<AgentChatSession>lambdaQuery()
                    .eq(AgentChatSession::getChatId, chatId)
                    .eq(AgentChatSession::getCreateUserId, CurrentUserHolder.getUserId())
                    .eq(AgentChatSession::getDeleted, DeletedTypeEnum.NOT_DELETED)
            );

            if (chatSession == null) {
                throw new BaseException(AgentOrchaMessageCode.CHAT_SESSION_NOT_FOUND, chatId);
            }

            // 更新已读状态
            if(chatSession.getUserReadStatus() == BooleanTypeEnum.FALSE){
                AgentChatSession update = new AgentChatSession()
                    .setId(chatSession.getId())
                    .setUpdateTime(chatSession.getUpdateTime())
                    .setUserReadStatus(BooleanTypeEnum.TRUE);
                agentChatSessionMapper.updateById(update);
            }
            log.info("标记会话已读成功，chatId: {}", chatId);

        } catch (Exception e) {
            log.error("标记会话已读失败，chatId: {}", chatId, e);
            throw e;
        }
    }
}
