package cn.genn.ai.agent.orcha.framework.execution;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.api.dto.message.FinishMessage;
import cn.genn.ai.agent.orcha.api.dto.message.TextMessage;
import cn.genn.ai.agent.orcha.biz.chat.AgentMessageManager;
import cn.genn.ai.agent.orcha.framework.cache.CancellationSignalManager;
import cn.genn.ai.agent.orcha.framework.cache.RedisStreamManager;
import cn.genn.spring.boot.starter.upm.component.MagicTokenContext;
import cn.genn.ai.agent.orcha.framework.persistence.converter.AgentMessageConverter;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatMessage;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatSession;
import cn.genn.ai.agent.orcha.framework.persistence.event.MessagePersistenceEvent;
import cn.genn.ai.agent.orcha.framework.persistence.service.ChatSessionService;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.genn.ai.agent.orcha.shared.enums.ExecutionMode;
import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import cn.genn.ai.agent.orcha.shared.exception.AgentOrchaMessageCode;
import cn.genn.core.exception.BaseException;
import cn.genn.lock.base.LockTemplate;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventPublish;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 执行引擎抽象基类 (Redis Stream重构版)
 * <p>
 * 核心改进：彻底分离后台任务和客户端流，确保网络中断不会影响后台任务执行
 *
 * @param <T> 下游原始事件类型
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public abstract class AbstractExecutionEngine<T> implements ExecutionEngine {

    @Resource
    private AgentMessageManager messageManager;
    @Resource
    private RedisStreamManager redisStreamManager;
    @Resource
    private LockTemplate lockTemplate;
    @Resource
    private CancellationSignalManager cancellationSignalManager;
    @Resource
    private SpringEventPublish eventPublisher;
    @Resource
    private ChatSessionService chatSessionService;
    @Resource
    private AgentMessageConverter agentMessageConverter;
    @Resource
    private GlobalHeartbeatManager heartbeatManager;

    private static final String TASK_LOCK_PREFIX = "GENN:AI:ORCHA:TASK-LOCK:";

    @Override
    public final Flux<AgentMessage> executeStream(ExecutionRequest request) {
        validateRequest(request);

        if (StrUtil.isBlank(request.getTaskId())) {
            // 1. 没有taskId：始终是新对话，开启新任务
            log.info("启动新任务（无taskId）。ChatId: {}", request.getChatId());
            return startNewWorkflow(request);
        } else {
            // 2. 有taskId：根据是否有lastEventId和任务状态决定
            log.info("处理已有任务。TaskId: {}, LastEventId: {}", request.getTaskId(), request.getLastEventId());
            return handleExistingTask(request);
        }
    }

    /**
     * 处理已存在的任务
     */
    private Flux<AgentMessage> handleExistingTask(ExecutionRequest request) {
        String taskId = request.getTaskId();
        String lastEventId = request.getLastEventId();

        try {
            TaskStatus taskStatus = messageManager.getTaskStatus(taskId);

            if (StrUtil.isNotBlank(lastEventId)) {
                // 2.1 有lastEventId：断线重连，直接从该位置开始读取
                log.info("断线重连场景，TaskId: {}, LastEventId: {}, 当前状态: {}",
                    taskId, lastEventId, taskStatus);
                return createMessageStreamForClient(taskId, lastEventId);

            } else {
                // 2.2 没有lastEventId：根据任务状态决定
                return handleTaskWithoutLastEventId(request, taskStatus);
            }

        } catch (Exception e) {
            log.error("处理已存在任务失败。TaskId: {}", taskId, e);
            return Flux.just(createErrorMessage(request, "处理任务失败"));
        }
    }

    /**
     * 处理没有lastEventId的任务请求
     */
    private Flux<AgentMessage> handleTaskWithoutLastEventId(ExecutionRequest request, TaskStatus taskStatus) {
        String taskId = request.getTaskId();

        if (taskStatus == TaskStatus.RUNNING) {
            // 任务正在运行，但客户端没有提供lastEventId，这是异常情况
            String errorMsg = String.format("任务正在运行中（TaskId: %s），但未提供lastEventId，请检查客户端状态", taskId);
            log.error(errorMsg);
            return Flux.just(createErrorMessage(request, "任务正在运行中,请勿重新发起"));

        } else if (taskStatus == TaskStatus.COMPLETED ||
            taskStatus == TaskStatus.FAILED ||
            taskStatus == TaskStatus.CANCELLED) {
            // 任务已结束，开始新任务（但复用taskId）
            log.info("任务已结束（状态：{}），重新启动任务。TaskId: {}", taskStatus, taskId);
            return restartTaskWorkflow(request);

        } else {
            // 其他状态（如null或其他未知状态），开始新任务
            log.info("任务状态为 {}，重新启动任务。TaskId: {}", taskStatus, taskId);
            return restartTaskWorkflow(request);
        }
    }

    /**
     * 重新启动任务工作流（复用taskId）
     */
    private Flux<AgentMessage> restartTaskWorkflow(ExecutionRequest request) {
        String taskId = request.getTaskId();
        String lockKey = TASK_LOCK_PREFIX + taskId;
        // 在异步执行前保存 MagicToken
        String magicToken = MagicTokenContext.getTokenData();
        if (magicToken != null && request.getMagicToken() == null) {
            request.setMagicToken(magicToken); // 确保 request 中有 magicToken
        }

        return Mono.fromCallable(() -> {
                // 使用分布式锁确保只有一个线程能重启任务
                var lockInfo = lockTemplate.lock(lockKey, 30000, 100);
                if (lockInfo == null) {
                    throw new RuntimeException("获取任务锁失败，请稍后重试");
                }

                try {
                    // 双重检查，防止在获取锁期间任务状态已变更
                    TaskStatus currentStatus = messageManager.getTaskStatus(taskId);
                    if (currentStatus == TaskStatus.RUNNING) {
                        throw new IllegalStateException("任务已在运行中，无法重新启动");
                    }

                    // 清理旧的取消信号
                    cancellationSignalManager.clearCancellationSignal(taskId);

                    // 重新初始化聊天会话
                    chatSessionService.initializeChatSession(request);
                    log.info("重新启动任务，TaskId: {}", taskId);

                    // 发布创建消息记录事件
                    publishMessagePersistenceEvent(request, taskId, TaskStatus.RUNNING, MessagePersistenceEvent.OperationType.CREATE);

                    // 启动独立的后台任务
                    restartIndependentBackgroundTask(request, magicToken);

                    return taskId;

                } finally {
                    lockTemplate.releaseLock(lockInfo);
                }
            })
            .subscribeOn(Schedulers.boundedElastic())
            .flatMapMany(restarted -> {
                // 从最新位置开始读取消息（不推送历史消息）
                return createMessageStreamForClient(taskId, "$");
            })
            .onErrorResume(e -> {
                log.error("重新启动任务失败，TaskId: {}", taskId, e);
                return Flux.just(createErrorMessage(request, "重新启动任务失败"));
            });
    }

    /**
     * 重新启动独立的后台任务
     */
    private void restartIndependentBackgroundTask(ExecutionRequest request, String magicToken) {
        Mono.fromRunnable(() -> {
                try {
                    Flux<T> independentRawStream = this.initiateRawStream(request);
                    Flux<T> cachedRawStream = independentRawStream.cache();

                    runCompletelyIndependentBackgroundTask(cachedRawStream, request.getChatId(), request.getTaskId(), request, magicToken);
                    log.info("独立后台任务重新启动完成，TaskId: {}", request.getTaskId());
                } catch (Exception e) {
                    log.error("重新启动独立后台任务失败。TaskId: {}", request.getTaskId(), e);
                    // 更新任务状态为失败
                    messageManager.updateTaskStatus(request.getChatId(), request.getTaskId(), TaskStatus.FAILED);
                    addFinishMessageToStream(request.getTaskId(), TaskStatus.FAILED);
                }
            })
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe();
    }


    /**
     * 处理新任务流程 - 关键改进：彻底分离后台任务和客户端流
     */
    private Flux<AgentMessage> startNewWorkflow(ExecutionRequest request) {
        // 在异步执行前保存 MagicToken
        String magicToken = MagicTokenContext.getTokenData();
        if (magicToken != null && request.getMagicToken() == null) {
            request.setMagicToken(magicToken); // 确保 request 中有 magicToken
        }

        return Mono.fromCallable(() -> {
                request.setNewTask(true);
                // 第一步：初始化聊天会话（在任务初始化提交时就先入库）
                chatSessionService.initializeChatSession(request);
                log.info("聊天会话初始化完成，chatId: {}", request.getChatId());

                // 第二步：立即启动完全独立的后台任务
                String taskId = startIndependentBackgroundTask(request, magicToken);
                log.info("后台任务已启动，TaskId: {}", taskId);

                // 第三步：设置request的taskId
                request.setTaskId(taskId);

                // 第四步：发布创建消息记录事件
                publishMessagePersistenceEvent(request, taskId, TaskStatus.RUNNING, MessagePersistenceEvent.OperationType.CREATE);

                return taskId;
            })
            .subscribeOn(Schedulers.boundedElastic()) // 在后台线程执行
            .flatMapMany(taskId -> {
                // 第三步：返回完全独立的客户端消息流
                return createMessageStreamForClient(taskId, "0-0");
            })
            .onErrorResume(e -> {
                log.error("启动新任务失败", e);
                return Flux.just(createErrorMessage(request, "启动新任务失败"));
            });
    }

    /**
     * 启动完全独立的后台任务
     * 这个方法会立即返回taskId，后台任务在独立的线程中执行，不受客户端连接状态影响
     */
    private String startIndependentBackgroundTask(ExecutionRequest request, String magicToken) {
        try {
            // 创建完全独立的原始流
            Flux<T> independentRawStream = this.initiateRawStream(request);

            // 使用 cache() 确保流被缓存，不受下游订阅状态影响
            Flux<T> cachedRawStream = independentRawStream
                .cache() // 关键：缓存整个流，确保即使客户端断开连接，数据仍然可用
                .doOnSubscribe(sub -> log.info("独立后台任务开始订阅原始流"))
                .doOnCancel(() -> log.warn("独立后台任务的原始流被取消"))
                .doOnComplete(() -> log.info("独立后台任务的原始流完成"))
                .doOnError(e -> log.error("独立后台任务的原始流出错", e));

            // 从缓存的流中获取第一个事件以解析taskId
            T firstEvent = cachedRawStream.blockFirst(); // 这里会阻塞，但是在后台线程中
            if (firstEvent == null) {
                throw new RuntimeException("下游服务未返回任何数据");
            }

            String taskId = this.extractTaskIdFromFirstEvent(firstEvent);
            if (taskId == null) {
                throw new RuntimeException("未能从下游服务解析taskId");
            }

            // 启动完全独立的后台处理任务
            runCompletelyIndependentBackgroundTask(cachedRawStream, request.getChatId(), taskId, request, magicToken);

            return taskId;

        } catch (Exception e) {
            log.error("启动独立后台任务失败", e);
            throw new RuntimeException("启动后台任务失败", e);
        }
    }

    /**
     * 运行完全独立的后台任务
     * 这个任务完全独立于客户端连接状态，使用独立的线程和订阅
     */
    private void runCompletelyIndependentBackgroundTask(Flux<T> cachedRawStream, String chatId, String taskId, ExecutionRequest request, String magicToken) {
        Mono.fromRunnable(() -> {
                log.info("开始执行完全独立的后台任务。TaskId: {}", taskId);

                // 在异步线程中恢复 MagicToken
                if (magicToken != null) {
                    MagicTokenContext.setTokenData(magicToken);
                    log.debug("在后台任务中恢复 MagicToken: {}", magicToken);
                }

                // 更新任务状态为运行中
                messageManager.updateTaskStatus(chatId, taskId, TaskStatus.RUNNING);

                // 创建支持取消的独立订阅
                this.processStream(cachedRawStream, request, chatId, taskId)
                    .takeWhile(message -> {
                        // 在处理每个消息前检查是否被取消
                        if (cancellationSignalManager.isCancelled(taskId)) {
                            log.info("检测到取消信号，停止处理消息。TaskId: {}", taskId);
                            return false;
                        }
                        return true;
                    })
                    .doOnNext(messages -> {
                        try {
                            // 在处理每批消息前，确保 MagicToken 在 ThreadLocal 中
                            if (magicToken != null) {
                                MagicTokenContext.setTokenData(magicToken);
                            }

                            // 再次检查取消信号
                            if (cancellationSignalManager.isCancelled(taskId)) {
                                log.info("处理消息时检测到取消信号。TaskId: {}", taskId);
                                return;
                            }
                            if (CollUtil.isEmpty(messages)) {
                                return;
                            }
                            messages.forEach(message -> {
                                messageManager.storeMessage(message);
                            });
                        } catch (Exception e) {
                            log.error("存储消息失败，TaskId: {}", taskId, e);
                        }
                    })
                    .doOnComplete(() -> {
                        if (cancellationSignalManager.isCancelled(taskId)) {
                            log.info("独立后台任务被取消。TaskId: {}", taskId);
                            messageManager.updateTaskStatus(chatId, taskId, TaskStatus.CANCELLED);
                            addFinishMessageToStream(taskId, TaskStatus.CANCELLED);
                            publishMessagePersistenceEvent(request, taskId, TaskStatus.CANCELLED);
                        } else {
                            log.info("独立后台任务执行完成。TaskId: {}", taskId);
                            messageManager.updateTaskStatus(chatId, taskId, TaskStatus.COMPLETED);
                            addFinishMessageToStream(taskId, TaskStatus.COMPLETED);
                            publishMessagePersistenceEvent(request, taskId, TaskStatus.COMPLETED);
                        }
                    })
                    .doOnError(error -> {
                        log.error("独立后台任务执行失败。TaskId: {}", taskId, error);
                        messageManager.updateTaskStatus(chatId, taskId, TaskStatus.FAILED);
                        addFinishMessageToStream(taskId, TaskStatus.FAILED);
                        publishMessagePersistenceEvent(request, taskId, TaskStatus.FAILED);
                    })
                    .doFinally(signalType -> {
                        // 使用异步方式清理取消信号，避免线程中断问题
                        cancellationSignalManager.clearCancellationSignalAsync(taskId);
                    })
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe(
                        message -> {
                        },
                        error -> {
                        },
                        () -> {
                        }
                    );
            })
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe();
    }

    /**
     * 为客户端创建基于Redis Stream的消息流
     * 这个流完全独立于后台任务，只负责从Redis Stream读取消息
     */
    private Flux<AgentMessage> createMessageStreamForClient(String taskId, String lastMessageId) {
        return Flux.<AgentMessage>create(sink -> {
                heartbeatManager.registerActiveTask(taskId);
                AtomicReference<String> currentLastId = new AtomicReference<>(
                    StrUtil.isBlank(lastMessageId) ? "0-0" : lastMessageId
                );

                AtomicBoolean isCancelled = new AtomicBoolean(false);
                AtomicLong lastMessageTime = new AtomicLong(System.currentTimeMillis());

                // 设置超时时间，比如10分钟没有新消息就认为任务已死
                long timeoutMs = 20 * 60 * 1000;

                sink.onDispose(() -> {
                    log.info("客户端取消订阅，将停止轮询。TaskId: {}", taskId);
                    isCancelled.set(true);
                    heartbeatManager.unregisterTask(taskId);
                });

                Schedulers.boundedElastic().schedule(() -> {
                    while (!isCancelled.get()) {
                        try {
                            if (currentLastId.get() == null) {
                                log.warn("当前lastId为空，无法继续读取消息。TaskId: {}", taskId);
                                heartbeatManager.unregisterTask(taskId);
                                sink.error(new BaseException(AgentOrchaMessageCode.REDIS_STREAM_READ_ERROR));
                                return;
                            }
                            // 检查超时
                            if (System.currentTimeMillis() - lastMessageTime.get() > timeoutMs) {
                                log.warn("任务超时，没有新消息超过{}ms，关闭流。TaskId: {}", timeoutMs, taskId);
                                heartbeatManager.unregisterTask(taskId);
                                sink.complete();
                                return;
                            }
                            List<MapRecord<String, Object, Object>> records =
                                redisStreamManager.readMessages(taskId, currentLastId.get(), 5000);

                            if (isCancelled.get()) {
                                break;
                            }

                            if (records.isEmpty()) {
                                continue;
                            }

                            lastMessageTime.set(System.currentTimeMillis());

                            for (MapRecord<String, Object, Object> record : records) {
                                Map<Object, Object> value = record.getValue();

                                AgentMessage message = agentMessageConverter.mapRecordToAgentMessage(record);

                                if ("finish".equals(value.get("cardType"))) {
                                    log.info("收到任务结束信号，关闭客户端流。TaskId: {}", taskId);
                                    heartbeatManager.unregisterTask(taskId);
                                    sink.next(message);
                                    sink.complete();
                                    return;
                                }

                                if (message != null) {
                                    sink.next(message);
                                }

                                currentLastId.set(record.getId().getValue());
                            }
                        } catch (Exception e) {
                            log.error("读取Stream消息失败，TaskId: {}", taskId, e);
                            heartbeatManager.unregisterTask(taskId);
                            sink.error(e);
                            return;
                        }
                    }
                    log.info("轮询循环已终止。TaskId: {}", taskId);
                    heartbeatManager.unregisterTask(taskId);
                });
            })
            .doOnCancel(() -> {
                heartbeatManager.unregisterTask(taskId);
                log.info("客户端取消订阅，流关闭。TaskId: {}", taskId);
            })
            .doOnComplete(() -> {
                heartbeatManager.unregisterTask(taskId);
                log.info("任务流正常结束。TaskId: {}", taskId);
            })
            .doOnError(error -> {
                heartbeatManager.unregisterTask(taskId);
                log.error("消息流发生错误。TaskId: {}", taskId, error);
            });
    }

    /**
     * 添加结束消息到Stream中
     */
    private void addFinishMessageToStream(String taskId, TaskStatus taskStatus) {
        try {
            AgentMessage finishMessage = new AgentMessage()
                .setTaskId(taskId)
                .setCardId(IdUtil.fastSimpleUUID())
                .setStarted(System.currentTimeMillis() / 1000)
                .setCardType(CardType.FINISH)
                .setMessages(List.of(new FinishMessage()
                    .setTaskStatus(taskStatus)
                    .setText(taskStatus.getDescription())))
                .setStatus(0);
            Map<String, String> finishMessageMap = agentMessageConverter.agentMessageToMap(finishMessage);
            redisStreamManager.addMessage(taskId, finishMessageMap);
            log.debug("结束消息已添加到Stream，TaskId: {}, Status: {}", taskId, taskStatus);
        } catch (Exception e) {
            log.error("添加结束消息到Stream失败，TaskId: {}", taskId, e);
        }
    }

    @Override
    public boolean stopExecution(String chatId) {
        log.info("收到停止执行请求，chatId: {}", chatId);
        AgentChatSession chatSession = chatSessionService.getChatSession(chatId);
        //找到最近一条正在运行的任务
        AgentChatMessage lastedMessage = messageManager.getLastedMessage(chatId);
        if (lastedMessage == null) {
            throw new BaseException(AgentOrchaMessageCode.TASK_NOT_FOUND);
        }
        String taskId = lastedMessage.getTaskId();

        try {
            // 1. 设置取消信号
            cancellationSignalManager.setCancellationSignal(taskId);

            // 2. 更新任务状态为已取消
            messageManager.updateTaskStatus(chatId, taskId, TaskStatus.CANCELLED);

            // 3. 调用子类实现的停止逻辑（取消下游请求）
            boolean downstreamStopped = doStopExecution(chatSession.getAppId(), taskId);

            // 4. 添加结束消息到Stream，通知客户端
            addFinishMessageToStream(taskId, TaskStatus.CANCELLED);

            log.info("停止执行完成，taskId: {}, 下游停止结果: {}", taskId, downstreamStopped);
            return true;

        } catch (Exception e) {
            log.error("停止执行失败，taskId: {}", taskId, e);
            return false;
        }
    }

    @Override
    public boolean supports(ExecutionRequest request) {
        return getSupportedMode().equals(request.getMode());
    }

    // ========== 抽象方法，由子类实现 ==========
    protected abstract Flux<T> initiateRawStream(ExecutionRequest request);

    protected abstract String extractTaskIdFromFirstEvent(T firstEvent);

    protected abstract Flux<List<AgentMessage>> processStream(Flux<T> rawStream, ExecutionRequest request, String chatId, String taskId);

    protected abstract ExecutionMode getSupportedMode();

    protected abstract boolean doStopExecution(String appId, String taskId);

    // ========== 通用方法 ==========
    protected void validateRequest(ExecutionRequest request) {
        if (request == null) throw new IllegalArgumentException("执行请求不能为空");
        if (request.getMode() == null) throw new IllegalArgumentException("执行模式不能为空");
        if (request.getInput() == null || request.getInput().trim().isEmpty())
            throw new IllegalArgumentException("用户输入不能为空");
    }

    protected AgentMessage createErrorMessage(ExecutionRequest request, String message) {
        return new AgentMessage()
            .setCardType(CardType.ERROR)
            .setMessages(List.of(new TextMessage().setText(message)))
            .setTaskId(request.getTaskId())
            .setStatus(0);
    }

    /**
     * 发布消息入库事件
     */
    private void publishMessagePersistenceEvent(ExecutionRequest request, String taskId, TaskStatus taskStatus) {
        publishMessagePersistenceEvent(request, taskId, taskStatus, MessagePersistenceEvent.OperationType.UPDATE);
    }

    /**
     * 发布消息入库事件（支持操作类型）
     */
    private void publishMessagePersistenceEvent(ExecutionRequest request, String taskId, TaskStatus taskStatus, MessagePersistenceEvent.OperationType operationType) {
        try {
            eventPublisher.publish(MessagePersistenceEvent.fromExecutionRequest(request, taskStatus, operationType, this));
            log.info("发布消息入库事件，taskId: {}, taskStatus: {}, operationType: {}", taskId, taskStatus, operationType);
        } catch (Exception e) {
            log.error("发布消息入库事件失败，taskId: {}, operationType: {}", taskId, operationType, e);
        }
    }


}
