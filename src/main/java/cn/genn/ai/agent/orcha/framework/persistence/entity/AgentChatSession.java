package cn.genn.ai.agent.orcha.framework.persistence.entity;

import cn.genn.ai.agent.orcha.shared.enums.ChatMode;
import cn.genn.ai.agent.orcha.shared.enums.ChatSourceType;
import cn.genn.core.model.enums.BooleanTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户与Agent的聊天会话记录表
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@TableName(value = "agent_chat_session", autoResultMap = true)
public class AgentChatSession {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 会话唯一ID
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * 关联的工作流id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 会话标题（通常是用户的第一个问题）
     */
    @TableField("title")
    private String title;

    @TableField("chat_mode")
    private ChatMode chatMode;

    /**
     * 额外配置，例如会话上下文、变量等
     */
    @TableField("extra_config")
    private String extraConfig;

    /**
     * 是否置顶
     */
    @TableField("pinned")
    private BooleanTypeEnum pinned;

    /**
     * 对话来源类型
     */
    @TableField("source_type")
    private ChatSourceType sourceType;

    /**
     * 用户是否已读（仅对定时任务生成的对话有效）
     */
    @TableField("user_read_status")
    private BooleanTypeEnum userReadStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedTypeEnum deleted;}
