package cn.genn.ai.agent.orcha.shared.third.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AgentFeedbackQuery {

    @Schema(description = "智能体ID")
    private String workflowId;

    @Schema(description = "会话id")
    private String chatId;

    @Schema(description = "单轮对话id")
    private String taskId;

    @Schema(description = "用户ID")
    private Long createUserId;

    @Schema(description = "是否仅查询我的反馈")
    private boolean onlyMyFeedback = true;
}
