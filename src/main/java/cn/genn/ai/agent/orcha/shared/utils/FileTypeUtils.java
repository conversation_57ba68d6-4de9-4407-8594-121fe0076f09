package cn.genn.ai.agent.orcha.shared.utils;

import lombok.Data;
import org.springframework.ai.content.Media;
import org.springframework.util.MimeType;

import java.net.URI;

/**
 * <AUTHOR>
 */
public class FileTypeUtils {

    /**
     * 判断URL对应的文件类型
     * @param urlStr URL字符串
     */
    public static FileType parseFileType(String urlStr) {
        try {
            URI uri = new URI(urlStr);
            String path = uri.getPath();
            if (path == null || path.isEmpty()) {
                return FileType.UNKNOWN;
            }
            // 转换为小写进行比较
            path = path.toLowerCase();
            // 检查常见图片文件扩展名
            if (path.endsWith(".jpg") || path.endsWith(".jpeg")
                || path.endsWith(".png") || path.endsWith(".gif")
                || path.endsWith(".bmp") || path.endsWith(".webp")
                || path.endsWith(".svg") || path.endsWith(".tiff")
                || path.endsWith(".ico")) {
                return FileType.IMAGE;
            }
            return FileType.FILE;
        } catch (Exception e) {
            return FileType.UNKNOWN;
        }
    }

    public static FileType parseFileType(FileInfo fileInfo) {
        if (fileInfo == null || (fileInfo.getContentType() == null && fileInfo.getFileUrl() == null)) {
            return FileType.UNKNOWN;
        }

        if (fileInfo.getContentType() != null) {
            String contentType = fileInfo.getContentType().toLowerCase();
            if (contentType.startsWith("image/")) {
                return FileType.IMAGE;
            } else if (!contentType.isEmpty()) {
                return FileType.FILE;
            }
        }

        // 如果contentType为空，则尝试通过URL后缀判断
        if (fileInfo.getFileUrl() != null) {
            return parseFileType(fileInfo.getFileUrl());
        }
        return FileType.UNKNOWN;
    }

    public static MimeType determineImageMimeType(String url) {
        String lowercaseUrl = url.toLowerCase();

        // 根据文件扩展名判断图像类型
        if (lowercaseUrl.endsWith(".jpg") || lowercaseUrl.endsWith(".jpeg")) {
            return Media.Format.IMAGE_JPEG;
        } else if (lowercaseUrl.endsWith(".png")) {
            return Media.Format.IMAGE_PNG;
        } else if (lowercaseUrl.endsWith(".gif")) {
            return Media.Format.IMAGE_GIF;
        } else if (lowercaseUrl.endsWith(".webp")) {
            return Media.Format.IMAGE_WEBP;
        }

        // 如果无法确定类型，默认使用JPEG
        return Media.Format.IMAGE_JPEG;
    }


    /**
     * 文件类型枚举
     */
    public enum FileType {
        IMAGE,
        FILE,
        UNKNOWN
    }

    /**
     * 文件信息
     */
    @Data
    public static class FileInfo {
        /**
         * 文件链接
         */
        private String fileUrl;

        /**
         * 文件类型
         */
        private String contentType;
    }

}
