package cn.genn.ai.agent.orcha.api.dto.message;

import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "结束消息")
public class FinishMessage {

    private TaskStatus taskStatus;

    private String text;
}
