package cn.genn.ai.agent.orcha.shared.third;

import cn.genn.ai.agent.orcha.api.dto.GroupAgentFeedbackDTO;
import cn.genn.ai.agent.orcha.api.dto.UserFeedbackRequest;
import cn.genn.ai.agent.orcha.shared.third.dto.AgentFeedbackQuery;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.util.List;
import java.util.Map;

/**
 * Genn AI Hub客户端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GennAIHubClient extends GennBaseClient {

    private final RestClient aiHubRestClient;

    public static final String GO_VOW_AGENT_LIST = "/toolInfo/goVow/agentList";
    public static final String FEEDBACK_LIST = "/agent/feedback/list";
    private static final String FEEDBACK_CREATE = "/agent/feedback/create";
    private static final String FEEDBACK_CANCEL = "/agent/feedback/cancel";

    /**
     * 获取格物智能体列表
     *
     * @return 智能体配置映射
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> getGoVowAgentConfig(String token) {
        try {
            // 先用String接收响应
            String response = aiHubRestClient
                .post()
                .uri(GO_VOW_AGENT_LIST)
                .contentType(MediaType.APPLICATION_JSON)
                .header("token", token)
                .retrieve()
                .body(String.class);
            return unWarp(response, Map.class, true);

        } catch (Exception e) {
            log.error("获取格物智能体列表失败", e);
            throw new RuntimeException("获取格物智能体列表失败", e);
        }
    }

    /**
     * 获取智能体反馈列表
     */
    public List<GroupAgentFeedbackDTO> fetchFeedbackList(String appId, String chatId, List<String> taskIds) {
        try {
            List<AgentFeedbackQuery> queryList = taskIds.stream().map(taskId ->
                new AgentFeedbackQuery()
                    .setWorkflowId(appId)
                    .setChatId(chatId)
                    .setTaskId(taskId)
                    .setCreateUserId(CurrentUserHolder.getUserId())).toList();

            // 先用String接收响应
            String response = aiHubRestClient
                .post()
                .uri(FEEDBACK_LIST)
                .contentType(MediaType.APPLICATION_JSON)
                .header("token", CurrentUserHolder.getToken())
                .body(queryList)
                .retrieve()
                .body(String.class);
            return unWarpList(response, GroupAgentFeedbackDTO.class, true);
        } catch (Exception e) {
            log.error("获取智能体反馈列表失败", e);
            throw new RuntimeException("获取智能体反馈列表失败", e);
        }
    }

    public boolean updateUserFeedback(UserFeedbackRequest request) {
        // 构建反馈请求的JSON参数
        String feedbackRequestBody = JsonUtils.toJsonNotNull(request);

        // 发送反馈请求
        String response = aiHubRestClient
            .post()
            .uri(FEEDBACK_CREATE)
            .header("token", CurrentUserHolder.getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .body(feedbackRequestBody)
            .retrieve()
            .body(String.class);
        log.debug("更新用户反馈成功，appId: {}, feedbackType: {}, response: {}",
            request.getAppId(), request.getFeedbackType(), response);

        return true;
    }

    /**
     * 取消智能体反馈
     *
     * @param id 反馈ID
     * @return 是否成功
     */
    public boolean cancelAgentFeedback(Long id) {
        try {
            String response = aiHubRestClient
                .post()
                .uri(FEEDBACK_CANCEL)
                .header("token", CurrentUserHolder.getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .body(Map.of("id", id))
                .retrieve()
                .body(String.class);

            log.debug("取消智能体反馈成功，id: {}, response: {}", id, response);
            return true;
        } catch (Exception e) {
            log.error("取消智能体反馈失败，id: {}", id, e);
            throw new RuntimeException("取消智能体反馈失败", e);
        }
    }
}
