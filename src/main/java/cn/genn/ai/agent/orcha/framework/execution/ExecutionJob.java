package cn.genn.ai.agent.orcha.framework.execution;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.message.FinishMessage;
import cn.genn.ai.agent.orcha.framework.cache.RedisStreamManager;
import cn.genn.ai.agent.orcha.framework.persistence.converter.AgentMessageConverter;
import cn.genn.ai.agent.orcha.framework.persistence.entity.AgentChatMessage;
import cn.genn.ai.agent.orcha.framework.persistence.event.MessagePersistenceEvent;
import cn.genn.ai.agent.orcha.framework.persistence.mapper.AgentChatMessageMapper;
import cn.genn.ai.agent.orcha.framework.persistence.service.MessagePersistenceService;
import cn.genn.ai.agent.orcha.shared.constants.CacheConstants;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.genn.ai.agent.orcha.shared.enums.TaskStatus;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 执行任务管理服务
 * 负责处理任务的生命周期管理，包括启动时清理孤儿任务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExecutionJob {

    private final StringRedisTemplate stringRedisTemplate;
    private final List<ExecutionEngine> executionEngines;
    private final AgentChatMessageMapper messageMapper;
    private final RedisStreamManager redisStreamManager;
    private final MessagePersistenceService messagePersistenceService;
    private final AgentMessageConverter agentMessageConverter;


    /**
     * 应用启动时清理孤儿任务
     * 从数据库查询所有运行中的任务，将其标记为失败并停止执行，防止僵尸任务
     */
    @PostConstruct
    public void cleanupOrphanedTasks() {
        try {
            log.info("开始清理孤儿任务...");

            // 从数据库查询所有运行中的任务
            List<AgentChatMessage> runningTasks = queryRunningTasks();

            if (runningTasks.isEmpty()) {
                log.info("没有发现运行中的任务，清理完成");
                return;
            }

            log.info("发现 {} 个运行中的任务，开始清理...", runningTasks.size());

            int orphanedCount = 0;

            for (AgentChatMessage task : runningTasks) {
                try {
                    String taskId = task.getTaskId();
                    String chatId = task.getChatId();

                    log.warn("发现孤儿任务: {}, chatId: {}", taskId, chatId);

                    // 1. 更新Redis中的任务状态
                    updateTaskStatusInRedis(taskId, TaskStatus.FAILED);

                    // 2. 发送结束信号到Stream
                    sendTaskFinishSignal(taskId, TaskStatus.FAILED.getCode(), "服务重启，任务终止");

                    // 3. 同步Redis Stream中的消息到数据库
                    syncStreamMessagesToDatabase(task);

                    // 4. 通过执行引擎停止任务
                    stopTaskExecution(chatId, taskId);

                    orphanedCount++;
                    log.info("已清理孤儿任务: {}", taskId);

                } catch (Exception e) {
                    log.error("处理孤儿任务失败: {}", task.getTaskId(), e);
                }
            }

            log.info("孤儿任务清理完成，共清理 {} 个任务", orphanedCount);

        } catch (Exception e) {
            log.error("清理孤儿任务失败", e);
        }
    }

    /**
     * 查询数据库中所有运行中的任务
     */
    private List<AgentChatMessage> queryRunningTasks() {
        LambdaQueryWrapper<AgentChatMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentChatMessage::getTaskStatus, TaskStatus.RUNNING)
            .select(AgentChatMessage::getId, AgentChatMessage::getTaskId,
                   AgentChatMessage::getChatId, AgentChatMessage::getCreateTime)
            .orderByDesc(AgentChatMessage::getCreateTime);

        return messageMapper.selectList(queryWrapper);
    }

    /**
     * 更新数据库中的任务状态
     */
    private void updateTaskStatusInDatabase(Long id, TaskStatus taskStatus) {
        try {
            AgentChatMessage updateMessage = new AgentChatMessage();
            updateMessage.setId(id);
            updateMessage.setTaskStatus(taskStatus);

            int updated = messageMapper.updateById(updateMessage);
            log.debug("更新数据库任务状态，id: {}, status: {}, 影响行数: {}",
                id, taskStatus, updated);
        } catch (Exception e) {
            log.error("更新数据库任务状态失败，id: {}", id, e);
        }
    }

    /**
     * 更新Redis中的任务状态
     */
    private void updateTaskStatusInRedis(String taskId, TaskStatus taskStatus) {
        try {
            String key = CacheConstants.buildAgentTaskStatusKey(taskId);
            stringRedisTemplate.opsForValue().set(key, taskStatus.getCode());
            log.debug("更新Redis任务状态，taskId: {}, status: {}", taskId, taskStatus);
        } catch (Exception e) {
            log.error("更新Redis任务状态失败，taskId: {}", taskId, e);
        }
    }

    /**
     * 通过执行引擎停止任务
     */
    private void stopTaskExecution(String chatId, String taskId) {
        try {
            for (ExecutionEngine executionEngine : executionEngines) {
                boolean stopped = executionEngine.stopExecution(chatId);
                if (stopped) {
                    log.debug("通过执行引擎停止任务成功，taskId: {}", taskId);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("通过执行引擎停止任务失败，taskId: {}", taskId, e);
        }
    }

    /**
     * 发送任务结束信号到Stream
     *
     * @param taskId 任务ID
     * @param status 任务状态
     * @param reason 结束原因
     */
    private void sendTaskFinishSignal(String taskId, String status, String reason) {
        try {
            AgentMessage finishMessage = new AgentMessage()
                .setTaskId(taskId)
                .setCardId(IdUtil.fastSimpleUUID())
                .setStarted(System.currentTimeMillis() / 1000)
                .setCardType(CardType.FINISH)
                .setMessages(List.of(new FinishMessage().setTaskStatus(TaskStatus.fromCode(status)).setText(reason)))
                .setStatus(0);
            Map<String, String> finishMessageMap =  agentMessageConverter.agentMessageToMap(finishMessage);
            String messageId = redisStreamManager.addMessage(taskId, finishMessageMap);

            if (messageId != null) {
                log.debug("结束信号已发送到Stream，taskId: {}, messageId: {}", taskId, messageId);
            } else {
                log.warn("发送结束信号失败，taskId: {}", taskId);
            }

        } catch (Exception e) {
            log.error("发送任务结束信号失败，taskId: {}", taskId, e);
        }
    }

    /**
     * 同步Redis Stream中的消息到数据库
     * 使用现有的MessagePersistenceService来处理Stream消息同步
     *
     * @param task 任务记录
     */
    private void syncStreamMessagesToDatabase(AgentChatMessage task) {
        try {
            String taskId = task.getTaskId();

            // 构建MessagePersistenceEvent来复用现有的消息处理逻辑
            MessagePersistenceEvent event = new MessagePersistenceEvent(this)
                .setOperationType(MessagePersistenceEvent.OperationType.UPDATE)
                .setTenantId(task.getTenantId())
                .setAppId(task.getAppId())
                .setChatId(task.getChatId())
                .setTaskId(taskId)
                .setFileInfo(task.getFileInfo())
                .setTaskStatus(TaskStatus.FAILED) // 僵尸任务标记为失败
                .setUserId(task.getCreateUserId())
                .setUserName(task.getCreateUserName())
                .setSessionTitle(task.getUserInput())
                .setExtraConfig(null);

            messagePersistenceService.processMessagePersistence(event);

            log.info("使用MessagePersistenceService同步Stream消息完成，taskId: {}",
                taskId);

        } catch (Exception e) {
            log.error("同步Stream消息到数据库失败，taskId: {}", task.getTaskId(), e);
        }
    }
}
