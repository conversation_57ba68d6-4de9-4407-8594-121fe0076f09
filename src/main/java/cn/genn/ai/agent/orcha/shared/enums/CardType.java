package cn.genn.ai.agent.orcha.shared.enums;

import cn.genn.ai.agent.orcha.framework.persistence.utils.message.DefaultMessageMergeStrategy;
import cn.genn.ai.agent.orcha.framework.persistence.utils.message.MessageMergeStrategy;
import cn.genn.ai.agent.orcha.framework.persistence.utils.message.TextMessageMergeStrategy;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 卡片类型枚举
 *
 * 定义SSE消息的卡片类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum CardType {

    /**
     * 心跳
     */
    PING("ping", "心跳消息", true,false, DefaultMessageMergeStrategy.class),

    /**
     * 助手回复
     */
    ASSISTANT("assistant", "助手回复", true,true, TextMessageMergeStrategy.class),

    /**
     * 问题拆解
     */
    QUESTION("question", "问题拆解", true,true, TextMessageMergeStrategy.class),

    /**
     * 工具调用
     */
    TOOL_CALL("toolCall", "工具调用", false,true,DefaultMessageMergeStrategy.class),

    /**
     * 思考过程
     */
    THINKING("thinking", "思考过程", true,true, TextMessageMergeStrategy.class),

    /**
     * 结构化输出
     */
    ARTIFACT_MESSAGE("artifactMessage", "结构化输出", false,true, DefaultMessageMergeStrategy.class),

    /**
     * 通知用户
     */
    NOTIFY_USER("notifyUser", "通知用户", false,true, DefaultMessageMergeStrategy.class),

    /**
     * 任务结束
     */
    FINISH("finish", "任务结束", false,true, DefaultMessageMergeStrategy.class),

    /**
     * 任务规划
     */
    STEP("step","任务规划", true,true, TextMessageMergeStrategy.class),

    /**
     * 任务总结
     */
    SUMMARY("summary", "任务总结", true,true, TextMessageMergeStrategy.class),

    /**
     * 搜索节点
     */
    SEARCH("search", "搜索节点", false,true, TextMessageMergeStrategy.class),

    /**
     * 网页读取节点
     */
    READ_URL("readUrl","网页读取", false, true, DefaultMessageMergeStrategy.class),

    /**
     * 交互式节点
     */
    INTERACTIVE("interactive", "交互式节点", false,true, DefaultMessageMergeStrategy.class),

    /**
     * 报告节点
     */
    REPORT("report", "报告节点", true,true, TextMessageMergeStrategy.class),

    /**
     * 分享URL
     */
    SHARE_URL("shareUrl", "分享URL", false,true, TextMessageMergeStrategy.class),

    /**
     * 下载url
     */
    DOWNLOAD_URL("downloadUrl", "下载URL", false,true, DefaultMessageMergeStrategy.class),

    /**
     * 搜索结果
     */
    RESEARCH_END("researchEnd", "搜索结束", true,true, TextMessageMergeStrategy.class),

    /**
     * 进度条
     */
    PROGRESS("progress", "进度条", false,true, DefaultMessageMergeStrategy.class),

    /**
     * 错误消息
     */
    ERROR("error", "错误消息", false,true, DefaultMessageMergeStrategy.class)
    ;

    /**
     * 类型代码
     */
    @JsonValue
    @EnumValue
    private final String code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 是否需要合并消息
     */
    private final boolean shouldMerge;

    /**
     * 是否入库
     */
    private final boolean isStore;

    /**
     * 合并策略类
     */
    private final Class<? extends MessageMergeStrategy> mergeStrategyClass;

    /**
     * 根据代码获取卡片类型
     */
    public static CardType fromCode(String code) {
        for (CardType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的卡片类型: " + code);
    }
}
