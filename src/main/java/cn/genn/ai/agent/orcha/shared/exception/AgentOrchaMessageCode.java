package cn.genn.ai.agent.orcha.shared.exception;

import cn.genn.core.exception.MessageCodeWrap;

/**
 * <AUTHOR>
 */
public enum AgentOrchaMessageCode implements MessageCodeWrap {

    WORKFLOW_NOT_FOUND("201", "工作流未找到"),
    TASK_ALREADY_RUNNING("301", "任务已在运行中，请稍后对话"),
    CHAT_ALREADY_RUNNING("302", "当前会话有未完成的任务，请稍后对话"),
    TASK_NOT_FOUND("303", "任务未找到,无法停止"),
    FAIL("500", "系统开小差了，请稍后再试"),
    DEEP_RESEARCH_MODE_LIMIT("304", "当前会话已达上限,请开启新会话"),
    CHAT_SESSION_NOT_FOUND("303", "会话未找到"),
    REDIS_STREAM_READ_ERROR("305", "消息读取失败");

    private final String code;
    private final String description;

    AgentOrchaMessageCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
