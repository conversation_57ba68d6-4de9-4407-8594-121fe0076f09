package cn.genn.ai.agent.orcha.framework.workflow.sse.converter.xml;

import cn.genn.ai.agent.orcha.api.dto.AgentMessage;
import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.api.dto.message.TextMessage;
import cn.genn.ai.agent.orcha.shared.enums.AreaType;
import cn.genn.ai.agent.orcha.shared.enums.CardType;
import cn.genn.ai.agent.orcha.shared.enums.ChatMode;
import cn.genn.ai.agent.orcha.shared.utils.XmlTagUtils;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class XmlTagParser {

    /**
     * 处理缓冲区内容
     */
    public List<AgentMessage> parse(String text, String taskId, ExecutionRequest request) {
        List<AgentMessage> messages = new ArrayList<>();

        // 获取缓冲区
        XmlTagBuffer buffer = XmlTagBufferManager.getXmlTagBuffer(taskId);
        buffer.appendContent(text);

        // 循环处理直到只剩不完整标签
        while (true) {
            String data = buffer.getBuffer();
            if (StrUtil.isEmpty(data)) {
                break;
            }

            // 解析XML元素
            List<XmlTagUtils.XmlElement> xmlElements = XmlTagUtils.parseTextSpecificTag(data, XmlTagType.getSupportedTagNames());

            if (xmlElements.isEmpty()) {
                break;
            }

            // 找到第一个完整的元素
            XmlTagUtils.XmlElement firstCompleteElement = findFirstCompleteElement(xmlElements);

            // 如果没有完整元素，停止处理等待更多数据
            if (firstCompleteElement == null) {
                break;
            }

            // 检查消消乐
            if (checkAndHandleElimination(buffer, xmlElements)) {
                continue;
            }

            // 处理第一个完整元素
            AgentMessage message = null;

            if (firstCompleteElement.isText()) {
                // 处理纯文本场景
                if (!XmlTagUtils.containsSpecificTags(xmlElements)) {
                    message = handleText(buffer, data, request);
                    if (message != null) {
                        messages.add(message);
                    }
                    buffer.clearBuffer();
                    break;
                } else {
                    // 处理文本部分
                    message = handleText(buffer, firstCompleteElement.getContent(), request);
                    if (message != null) {
                        messages.add(message);
                    }
                    removeProcessedElement(buffer, data, firstCompleteElement);
                }
            } else if (firstCompleteElement.isTag()) {
                if (firstCompleteElement.isLeftTag()) {
                    // 处理左标签
                    message = handleLeftTag(buffer, firstCompleteElement, request);
                    if (message != null) {
                        messages.add(message);
                    }
                    removeProcessedElement(buffer, data, firstCompleteElement);
                } else {
                    // 处理右标签
                    message = handleRightTag(buffer, firstCompleteElement, request);

                    // 检查是否有待发送的内容消息
                    if (buffer.hasPendingMessage()) {
                        AgentMessage pendingMessage = buffer.getPendingMessage();
                        messages.add(pendingMessage);
                    }

                    if (message != null) {
                        messages.add(message);
                    }
                    removeProcessedElement(buffer, data, firstCompleteElement);
                }
            }
        }

        return messages;
    }

    /**
     * 找到第一个完整的元素
     */
    private XmlTagUtils.XmlElement findFirstCompleteElement(List<XmlTagUtils.XmlElement> xmlElements) {
        for (XmlTagUtils.XmlElement element : xmlElements) {
            if (element.isText() || (element.isTag() && element.isComplete())) {
                return element;
            }
        }
        return null;
    }


    /**
     * 检查并处理消消乐
     */
    private boolean checkAndHandleElimination(XmlTagBuffer buffer, List<XmlTagUtils.XmlElement> xmlElements) {
        // 检查是否有连续的成对标签且中间没有内容
        for (int i = 0; i < xmlElements.size() - 1; i++) {
            XmlTagUtils.XmlElement current = xmlElements.get(i);
            XmlTagUtils.XmlElement next = xmlElements.get(i + 1);

            // 检查是否为成对的完整标签
            if (current.isTag() && current.isComplete() && current.isLeftTag() &&
                next.isTag() && next.isComplete() && !next.isLeftTag() &&
                current.getTagName().equals(next.getTagName())) {

                // 排除 PROGRESS 标签的消消乐
                XmlTagType tagType = XmlTagType.fromTagName(current.getTagName());
                if (XmlTagType.PROGRESS.equals(tagType)) {
                    continue; // 跳过 PROGRESS 标签的消消乐
                }

                // 消消乐：直接移除这对标签
                String remainingText = XmlTagUtils.truncateElements(buffer.getBuffer(),
                    xmlElements.subList(i, i + 2));
                buffer.setBuffer(remainingText);
                return true;
            }

            // 检查是否为成对标签但中间只有空白文本
            if (i < xmlElements.size() - 2) {
                XmlTagUtils.XmlElement middle = xmlElements.get(i + 1);
                XmlTagUtils.XmlElement third = xmlElements.get(i + 2);

                if (current.isTag() && current.isComplete() && current.isLeftTag() &&
                    middle.isText() && StrUtil.isEmpty(middle.getContent()) &&
                    third.isTag() && third.isComplete() && !third.isLeftTag() &&
                    current.getTagName().equals(third.getTagName())) {

                    // 消消乐：直接移除这对标签和中间的空白文本
                    String remainingText = XmlTagUtils.truncateElements(buffer.getBuffer(),
                        xmlElements.subList(i, i + 3));
                    buffer.setBuffer(remainingText);
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 处理文本内容
     */
    private AgentMessage handleText(XmlTagBuffer buffer, String content, ExecutionRequest request) {
        if (StrUtil.isEmpty(content)) {
            return null;
        }

        XmlTagBuffer.TagState currentTag = buffer.getCurrentTag();

        // 如果当前标签是PROGRESS，不处理内容，直接返回null
        if (currentTag != null && XmlTagType.PROGRESS.equals(currentTag.getTagType())) {
            return null;
        }

        // 如果当前标签需要等待完整内容，则累积内容而不立即返回
        if (currentTag != null && currentTag.isWaitForComplete()) {
            currentTag.appendContent(content);
            return null;
        }

        // 正常处理文本
        XmlTagType tagType = Optional.ofNullable(currentTag)
            .map(XmlTagBuffer.TagState::getTagType)
            .orElse(XmlTagType.DEFAULT);

        CardType cardType = tagType.getCardType();
        List<Object> messages = tagType.getMessageCreator().apply(content);

        if (messages == null || messages.isEmpty()) {
            return null;
        }

        return generateAgentMessage(buffer, cardType, messages, 1, currentTag, request);
    }

    /**
     * 处理左标签
     */
    private AgentMessage handleLeftTag(XmlTagBuffer buffer, XmlTagUtils.XmlElement element, ExecutionRequest request) {
        XmlTagType xmlTagType = XmlTagType.fromTagName(element.getTagName());

        //特殊处理,对于deepResearch模式下,首次交互,think标签开始时,将缓存标记设置为true
        // 一旦出现question标签,将缓存标记设置为false
        if ((XmlTagType.THINK.equals(xmlTagType) || XmlTagType.THINKING.equals(xmlTagType) || XmlTagType.STEP.equals(xmlTagType)) && request.isNewTask() && request.getChatMode() == ChatMode.DEEP_RESEARCH) {
            XmlTagBufferManager.setDeepResearchThinkAreaSign(request.getTaskId(), true);
        }
        if (XmlTagType.QUESTION.equals(xmlTagType) && request.getChatMode() == ChatMode.DEEP_RESEARCH) {
            XmlTagBufferManager.setDeepResearchThinkAreaSign(request.getTaskId(), false);
        }

        if (XmlTagType.RESEARCH_END.equals(xmlTagType)) {
            XmlTagBufferManager.setWorkspaceAreaSign(request.getTaskId(), false);
        }

        // PROGRESS标签特殊处理
        if (XmlTagType.PROGRESS.equals(xmlTagType)) {
            XmlTagBufferManager.setWorkspaceAreaSign(request.getTaskId(), true);

            // 推送进度0的card
            String cardId = generateCardId();
            List<Object> messages = List.of(new TextMessage().setText(""));

            AgentMessage progressMessage = generateAgentMessage(buffer, CardType.PROGRESS, messages, 1,
                buffer.getCurrentTag(), request, cardId);
            progressMessage.setProgress(0.0);

            // 压栈但不需要实际的标签状态，只是为了标记
            XmlTagBuffer.TagState progressTag = XmlTagBuffer.TagState.builder()
                .area(AreaType.WORKSPACE)
                .cardId(cardId)
                .tagType(xmlTagType)
                .waitForComplete(false)
                .build();

            buffer.pushTag(progressTag);

            return progressMessage;
        }

        // 生成cardId并压栈
        String cardId = generateCardId();
        AreaType area = xmlTagType.getAreaType();

        XmlTagBuffer.TagState parentTag = buffer.getCurrentTag();

        XmlTagBuffer.TagState newTag = XmlTagBuffer.TagState.builder()
            .area(area)
            .cardId(cardId)
            .tagType(xmlTagType)
            .waitForComplete(xmlTagType.isWaitForComplete())
            .build();

        buffer.pushTag(newTag);

        // 生成开始消息
        CardType cardType = xmlTagType.getCardType();
        List<Object> messages = List.of(new TextMessage().setText(""));

        return generateAgentMessage(buffer, cardType, messages, 1, parentTag, request, cardId);
    }

    /**
     * 处理右标签
     */
    private AgentMessage handleRightTag(XmlTagBuffer buffer, XmlTagUtils.XmlElement element, ExecutionRequest request) {
        XmlTagType xmlTagType = XmlTagType.fromTagName(element.getTagName());

        XmlTagBuffer.TagState currentTag = buffer.getCurrentTag();
        if (currentTag == null) {
            return null;
        }

        // 弹出标签
        buffer.popTag();
        XmlTagBuffer.TagState parentTag = buffer.getCurrentTag();

        // 检查标签是否匹配，不匹配则忽略
        if (!currentTag.getTagType().getTagName().equals(element.getTagName())) {
            return null;
        }

        // PROGRESS标签特殊处理
        if (XmlTagType.PROGRESS.equals(xmlTagType)) {
            XmlTagBufferManager.setWorkspaceAreaSign(request.getTaskId(), false);

            // 推送进度100的card
            List<Object> messages = List.of(new TextMessage().setText(""));

            AgentMessage progressMessage = generateAgentMessage(buffer, CardType.PROGRESS, messages, 0,
                parentTag, request, currentTag.getCardId());
            progressMessage.setProgress(100.0);

            return progressMessage;
        }

        // 如果是等待完整内容的标签，现在处理累积的内容
        if (currentTag.isWaitForComplete() && StrUtil.isNotEmpty(currentTag.getAccumulatedContent())) {
            // 处理累积的完整内容
            String accumulatedContent = currentTag.getAccumulatedContent();
            XmlTagType tagType = currentTag.getTagType();

            // 使用标签类型的消息创建函数处理完整内容
            List<Object> contentMessages = tagType.getMessageCreator().apply(accumulatedContent);

            if (contentMessages != null && !contentMessages.isEmpty()) {
                // 生成内容消息
                AgentMessage contentMessage = generateAgentMessage(buffer, tagType.getCardType(),
                    contentMessages, 1, parentTag, request);
                contentMessage.setParentCardId(currentTag.getCardId());

                // 将内容消息先存储，然后在外层处理
                buffer.setPendingMessage(contentMessage);
            }
        }

        // 生成结束消息
        CardType cardType = xmlTagType.getCardType();
        List<Object> messages = List.of(new TextMessage().setText(""));

        return generateAgentMessage(buffer, cardType, messages, 0, parentTag, request, currentTag.getCardId());
    }

    /**
     * 生成消息 - 带区域处理逻辑
     */
    private AgentMessage generateAgentMessage(XmlTagBuffer buffer, CardType cardType, List<Object> messages,
                                              Integer status, XmlTagBuffer.TagState parentTag, ExecutionRequest request) {
        return generateAgentMessage(buffer, cardType, messages, status, parentTag, request, null);
    }

    /**
     * 生成消息 - 带区域处理逻辑
     */
    private AgentMessage generateAgentMessage(XmlTagBuffer buffer, CardType cardType, List<Object> messages,
                                              Integer status, XmlTagBuffer.TagState parentTag, ExecutionRequest request, String cardId) {

        // 确定区域类型
        AreaType area = determineArea(buffer, cardType, request);

        // 确定父卡片ID
        String parentCardId = Optional.ofNullable(parentTag)
            .map(XmlTagBuffer.TagState::getCardId)
            .orElse(null);

        AgentMessage agentMessage = new AgentMessage()
            .setTaskId(buffer.getTaskId())
            .setCardId(cardId != null ? cardId : generateCardId())
            .setParentCardId(parentCardId)
            .setStarted(System.currentTimeMillis() / 1000)
            .setCardType(cardType)
            .setMessages(messages)
            .setStatus(status)
            .setArea(area);

        // 处理进度
        handleProgress(buffer, agentMessage, request);

        return agentMessage;
    }

    /**
     * 确定区域类型
     */
    private AreaType determineArea(XmlTagBuffer buffer, CardType cardType, ExecutionRequest request) {
        ChatMode chatMode = request.getChatMode();

        // PROGRESS标签始终在workspace区域
        if (cardType.equals(CardType.PROGRESS)) {
            return AreaType.WORKSPACE;
        }

        if (cardType.equals(CardType.SHARE_URL)) {
            return AreaType.CONTENT;
        }

        // 在深度研究模式下，如果在工作空间标记内，所有消息都是workspace区域
        Boolean areaSign = XmlTagBufferManager.getWorkspaceAreaSign(request.getTaskId());
        if (chatMode.equals(ChatMode.DEEP_RESEARCH) && areaSign) {
            return AreaType.WORKSPACE;
        }

        Boolean thinkAreaSign = XmlTagBufferManager.getDeepResearchThinkAreaSign(request.getTaskId());
        if (chatMode.equals(ChatMode.DEEP_RESEARCH) && thinkAreaSign && request.isNewTask()) {
            return AreaType.THINK;
        }


        // 思考类型显示在思考区域（除非在工作空间内）
        if (cardType.equals(CardType.THINKING)) {
            return AreaType.THINK;
        }

        // 获取当前标签的区域类型
        XmlTagBuffer.TagState currentTag = buffer.getCurrentTag();
        return Optional.ofNullable(currentTag)
            .map(XmlTagBuffer.TagState::getArea)
            .orElse(AreaType.CONTENT);
    }



    /**
     * 处理进度
     */
    private void handleProgress(XmlTagBuffer buffer, AgentMessage agentMessage, ExecutionRequest request) {
        ChatMode chatMode = request.getChatMode();
        if (chatMode.equals(ChatMode.DEEP_RESEARCH)) {
            Boolean areaSign = XmlTagBufferManager.getWorkspaceAreaSign(request.getTaskId());
            if (areaSign) {
                agentMessage.setProgress(buffer.getProgress());
            } else if (buffer.getProgress() != 100.0) {
                agentMessage.setProgress(100.0);
                buffer.setProgress(100.0);
            }
        }
    }

    /**
     * 移除已处理的元素
     */
    private void removeProcessedElement(XmlTagBuffer buffer, String data, XmlTagUtils.XmlElement element) {
        String remainingText = XmlTagUtils.truncateElements(data, Collections.singletonList(element));
        buffer.setBuffer(remainingText);
    }

    /**
     * 生成卡片ID
     */
    private String generateCardId() {
        return IdUtil.fastSimpleUUID();
    }
}
