package cn.genn.ai.agent.orcha.api.dto;

import cn.genn.ai.agent.orcha.shared.enums.FeedbackTypeEnum;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class AgentFeedbackDTO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "智能体ID")
    @JsonProperty("appId")  // 序列化时使用这个名称
    @JsonAlias({"appId", "workflowId"})  // 反序列化时这些名称都可以接受
    private String workflowId;

    @Schema(description = "会话id")
    private String chatId;

    @Schema(description = "单轮对话id")
    private String taskId;

    @Schema(description = "反馈类型: LIKE (赞)、DISLIKE (踩)、WORD_MARK（划词反馈）")
    private FeedbackTypeEnum feedbackType;

    @Schema(description = "反馈标签")
    private String feedbackTag;

    @Schema(description = "划词文本, 仅反馈类型为划词反馈时有值")
    private String wordMarkText;

    @Schema(description = "用户提供的具体原因")
    private String reason;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建用户ID (系统内部用户ID)")
    private Long createUserId;

    @Schema(description = "创建用户名 (系统内部用户名)")
    private String createUserName;

}
