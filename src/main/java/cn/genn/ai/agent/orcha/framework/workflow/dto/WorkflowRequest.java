package cn.genn.ai.agent.orcha.framework.workflow.dto;

import cn.genn.ai.agent.orcha.api.dto.ExecutionRequest;
import cn.genn.ai.agent.orcha.shared.enums.ChatMode;
import cn.genn.ai.agent.orcha.shared.enums.ChatSourceType;
import cn.genn.ai.agent.orcha.shared.utils.FileTypeUtils;
import cn.genn.spring.boot.starter.upm.model.SsoUserAuthInfoDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作流请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
public class WorkflowRequest {

    /**
     * 工作流ID
     */
    private String appId;

    /**
     * 聊天id
     */
    private String chatId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 是否是新任务
     */
    private boolean newTask;

    /**
     * 输入数据
     */
    private String input;

    /**
     * 消息列表（用于Cerebro API）
     */
    private List<WorkflowMessage> messages;

    /**
     * 变量（用于Cerebro API）
     */
    private Map<String, String> variables;

    /**
     * AI回复的dataId
     */
    private String responseChatItemId;

    /**
     * 是否返回详细信息
     */
    private Boolean detail = true;

    /**
     * 扩展参数
     */
    private Map<String, String> parameters;

    /**
     * 是否流式返回
     */
    private Boolean stream = true;

    /**
     * 超时时间（秒）
     */
    private Integer timeout = 300;

    private SsoUserAuthInfoDTO userInfo;

    @JsonIgnore
    private Map<String, String> httpHeaders;

    /**
     * 对话来源类型
     */
    private ChatSourceType source;

    /**
     * Magic Token
     */
    @JsonIgnore
    private String magicToken;

    /**
     * 创建一个新的工作流请求实例并初始化其属性。
     *
     * @param userRequest 用户的执行请求对象，用于提供所需的初始化数据，包括应用ID、聊天ID、任务ID、输入数据、
     *                    扩展参数和是否流式返回等。
     * @return 初始化后的工作流请求对象。
     */
    public static WorkflowRequest create(ExecutionRequest userRequest) {
        WorkflowRequest workflowRequest = new WorkflowRequest()
            .setAppId(userRequest.getAppId())
            .setChatId(userRequest.getChatId())
            .setTaskId(userRequest.getTaskId())
            .setNewTask(userRequest.isNewTask())
            .setInput(userRequest.getInput())
            .setResponseChatItemId(userRequest.getResponseChatItemId())
            .setParameters(userRequest.getParameters())
            .setUserInfo(userRequest.getUserInfo())
            .setHttpHeaders(userRequest.getHttpHeaders())
            .setSource(userRequest.getSource())
            .setStream(userRequest.getStream())
            .setMagicToken(userRequest.getMagicToken());

        //设置消息列表和变量
        workflowRequest.setMessages(List.of(new WorkflowMessage()
            .setRole("user")
            .setContent(buildContent(userRequest))));
        // 初始化变量
        workflowRequest.setVariables(createVariables(userRequest.getChatMode(),userRequest.getParameters()));
        return workflowRequest;
    }

    private static Object buildContent(ExecutionRequest userRequest) {
        if (CollUtil.isEmpty(userRequest.getFileInfo())) {
            return userRequest.getInput();
        }
        List<Content> fileContents = userRequest.getFileInfo().stream().map(fileInfo -> {
            FileTypeUtils.FileType fileType = FileTypeUtils.parseFileType(fileInfo.getUrl());
            if (fileType == FileTypeUtils.FileType.FILE) {
                return Content.builder()
                    .type(Content.Type.FILE_URL)
                    .url(fileInfo.getUrl())
                    .rawData(fileInfo.getRawData())
                    .name(String.valueOf(fileInfo.getRawData().getOrDefault("fileName", "")))
                    .build();
            }
            return Content.builder()
                .type(Content.Type.IMAGE_URL)
                .imageUrl(new Content.ImageUrl(fileInfo.getUrl()))
                .rawData(fileInfo.getRawData())
                .name(String.valueOf(fileInfo.getRawData().getOrDefault("fileName", "")))
                .build();
        }).collect(Collectors.toList());
        if (userRequest.getInput() != null) {
            fileContents.addLast(Content.builder().type(Content.Type.TEXT).text(userRequest.getInput()).build());
        }
        return fileContents;
    }

    private static Map<String, String> createVariables(ChatMode chatMode,Map<String, String> parameters) {
        Map<String, String> variables = new HashMap<>();
        if(CollUtil.isNotEmpty(parameters)){
            variables.putAll(parameters);
        }
        variables.put("cTime", DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss E"));
        switch (chatMode) {
            case ChatMode.DEEP_RESEARCH:
                variables.put("chatMode", "research");
        }
        return variables;
    }

}
