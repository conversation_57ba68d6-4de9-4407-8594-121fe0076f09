package cn.genn.ai.agent.orcha.api.dto;

import cn.genn.ai.agent.orcha.shared.enums.FeedbackTypeEnum;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 用户反馈请求 DTO
 * 用于调用 cerebro 接口的用户反馈数据
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "用户反馈请求")
public class UserFeedbackRequest {

    @Schema(description = "应用ID")
    @JsonProperty("workflowId")
    @JsonAlias({"appId", "workflowId"})
    private String appId;

    @Schema(description = "会话ID")
    @NotBlank(message = "会话ID不能为空")
    private String chatId;

    @Schema(description = "任务ID")
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "反馈类型: LIKE (赞)、DISLIKE (踩)、WORD_MARK（划词反馈）")
    private FeedbackTypeEnum feedbackType;

    @Schema(description = "反馈标签")
    private String feedbackTag;

    @Schema(description = "划词文本, 仅反馈类型为划词反馈时有值")
    private String wordMarkText;

    @Schema(description = "用户提供的具体原因")
    private String reason;

    @Schema(description = "反馈来源系统, 1: cerebro, 2: 企业大脑")
    private Integer sourceSystem;

    @Schema(description = "创建用户ID（当为外部系统时，为外部系统的用户唯一标识）")
    private Long createUserId;

    @Schema(description = "创建用户名")
    private String createUserName;
}
