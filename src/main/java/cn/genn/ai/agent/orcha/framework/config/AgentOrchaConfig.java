package cn.genn.ai.agent.orcha.framework.config;

import cn.genn.ai.agent.orcha.framework.config.properties.AgentOrchaProperties;
import cn.genn.spring.boot.starter.upm.component.MagicTokenClientRequestInterceptor;
import cn.genn.spring.boot.starter.upm.component.MagicTokenContext;
import cn.genn.spring.boot.starter.upm.component.MagicTokenRequestInterceptor;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.RestClient;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class AgentOrchaConfig {

    /**
     * 注入一个默认的 RestClient.Builder,防止覆盖底层的 RestClient.Builder
     */
    @Bean
    @Primary
    RestClient.Builder restClientBuilder(RestClientBuilderConfigurer configurer) {
        return configurer.configure(RestClient.builder());
    }

    @Bean("lbRestClientBuilder")
    @LoadBalanced
    RestClient.Builder lbRestClientBuilder(RestClientBuilderConfigurer configurer) {
        return configurer.configure(RestClient.builder());
    }

    @Bean
    public RestClient aiHubRestClient(@Qualifier("lbRestClientBuilder") RestClient.Builder lbBuilder, AgentOrchaProperties properties) {
        return lbBuilder.baseUrl(properties.getCerebro().getGennAIHubUrl())
            .requestInterceptor(new MagicTokenClientRequestInterceptor())
            .build();
    }
}
